from app import create_app
import os

app = create_app(os.getenv('FLASK_ENV', 'development'))

if __name__ == '__main__':
    try:
        print("正在启动Flask应用...")
        print("检查应用配置...")
        print(f"数据库URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
        print(f"Redis URL: {app.config['REDIS_URL']}")

        # 测试数据库连接
        with app.app_context():
            from app import db
            db.create_all()
            print("数据库连接成功")

        print("启动服务器...")
        app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()