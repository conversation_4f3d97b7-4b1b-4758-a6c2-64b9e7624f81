from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from flask_mail import Mail
from flask_bcrypt import Bcrypt
import redis
from config.config import config

# 初始化扩展
db = SQLAlchemy()
jwt = JWTManager()
mail = Mail()
bcrypt = Bcrypt()
redis_client = None

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    mail.init_app(app)
    bcrypt.init_app(app)
    
    # 初始化Redis
    global redis_client
    try:
        redis_client = redis.from_url(app.config['REDIS_URL'])
        # 测试Redis连接
        redis_client.ping()
        print("Redis连接成功")
    except Exception as e:
        print(f"Redis连接失败，将使用内存存储: {e}")
        redis_client = None
    
    # 启用CORS
    CORS(app, resources={r"/api/*": {"origins": "*"}})
    
    # 注册蓝图
    from app.routes.auth import auth_bp
    from app.routes.user import user_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(user_bp, url_prefix='/api/user')

    # 添加根路由
    @app.route('/')
    def index():
        return jsonify({
            "message": "家庭外卖系统 API",
            "version": "1.0.0",
            "status": "running"
        })

    # 创建数据库表
    with app.app_context():
        db.create_all()

    return app