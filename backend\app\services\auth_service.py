from app import db, bcrypt, redis_client, mail
from app.models.user import User, LoginType
from flask import current_app
from flask_mail import Message
from flask_jwt_extended import create_access_token, create_refresh_token, decode_token
import phonenumbers
import re
import random
import string
from datetime import datetime, timedelta
import requests
import json
import threading

# 内存存储作为Redis的备选方案，添加线程锁
_memory_store = {}
_memory_lock = threading.Lock()

class AuthService:
    """认证服务类"""
    
    @staticmethod
    def validate_email(email):
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone):
        """验证手机号格式"""
        try:
            parsed_number = phonenumbers.parse(phone, "CN")
            return phonenumbers.is_valid_number(parsed_number)
        except:
            return False
    
    @staticmethod
    def generate_verification_code():
        """生成6位验证码"""
        return ''.join(random.choices(string.digits, k=6))
    
    @staticmethod
    def send_email_verification(email, code):
        """发送邮箱验证码"""
        try:
            msg = Message(
                '家庭外卖系统 - 邮箱验证',
                sender=current_app.config['MAIL_USERNAME'],
                recipients=[email]
            )
            msg.body = f'您的验证码是：{code}，有效期10分钟。请勿泄露给他人。'
            mail.send(msg)
            return True
        except Exception as e:
            print(f"发送邮件失败: {e}")
            return False
    
    @staticmethod
    def send_sms_verification(phone, code):
        """发送短信验证码"""
        try:
            # 这里需要集成具体的短信服务商API
            # 示例使用阿里云短信服务
            url = "https://dysmsapi.aliyuncs.com/"
            data = {
                "PhoneNumbers": phone,
                "SignName": "家庭外卖",
                "TemplateCode": "SMS_123456789",
                "TemplateParam": json.dumps({"code": code})
            }
            # 实际实现需要添加签名等认证信息
            # response = requests.post(url, data=data)
            # return response.status_code == 200
            print(f"发送短信验证码到 {phone}: {code}")
            return True
        except Exception as e:
            print(f"发送短信失败: {e}")
            return False
    
    @staticmethod
    def store_verification_code(key, code, expire_minutes=10):
        """存储验证码到Redis或内存"""
        try:
            if redis_client:
                redis_client.setex(f"verification:{key}", expire_minutes * 60, code)
            else:
                # 使用内存存储作为备选方案，添加线程安全
                with _memory_lock:
                    expire_time = datetime.now() + timedelta(minutes=expire_minutes)
                    _memory_store[f"verification:{key}"] = {
                        'code': code,
                        'expire_time': expire_time
                    }
        except Exception as e:
            print(f"存储验证码失败: {e}")
            # 使用内存存储作为备选方案
            with _memory_lock:
                expire_time = datetime.now() + timedelta(minutes=expire_minutes)
                _memory_store[f"verification:{key}"] = {
                    'code': code,
                    'expire_time': expire_time
                }

    @staticmethod
    def verify_code(key, code):
        """验证验证码"""
        try:
            if redis_client:
                stored_code = redis_client.get(f"verification:{key}")
                if stored_code and stored_code.decode() == code:
                    redis_client.delete(f"verification:{key}")
                    return True
            else:
                # 使用内存存储，添加线程安全
                with _memory_lock:
                    stored_data = _memory_store.get(f"verification:{key}")
                    if stored_data and stored_data['code'] == code:
                        if datetime.now() < stored_data['expire_time']:
                            del _memory_store[f"verification:{key}"]
                            return True
                        else:
                            # 验证码已过期
                            del _memory_store[f"verification:{key}"]
            return False
        except Exception as e:
            print(f"验证验证码失败: {e}")
            # 使用内存存储作为备选方案
            with _memory_lock:
                stored_data = _memory_store.get(f"verification:{key}")
                if stored_data and stored_data['code'] == code:
                    if datetime.now() < stored_data['expire_time']:
                        del _memory_store[f"verification:{key}"]
                        return True
                    else:
                        # 验证码已过期
                        del _memory_store[f"verification:{key}"]
            return False

    @staticmethod
    def refresh_access_token(refresh_token):
        """刷新访问令牌"""
        try:
            # 解码刷新令牌
            decoded = decode_token(refresh_token)
            user_id = decoded['sub']
            
            # 验证用户是否存在
            user = User.query.get(user_id)
            if not user or not user.is_active:
                return {"success": False, "message": "用户不存在或已被禁用"}
            
            # 生成新的访问令牌
            new_access_token = create_access_token(identity=user_id)
            
            return {
                "success": True,
                "message": "令牌刷新成功",
                "access_token": new_access_token
            }
        except Exception as e:
            print(f"刷新令牌失败: {e}")
            return {"success": False, "message": "刷新令牌无效或已过期"}
    
    @staticmethod
    def register_with_email(email, password, username=None):
        """邮箱注册"""
        if not AuthService.validate_email(email):
            return {"success": False, "message": "邮箱格式不正确"}
        
        if User.query.filter_by(email=email).first():
            return {"success": False, "message": "邮箱已被注册"}
        
        if not username:
            username = email.split('@')[0]
        
        # 检查用户名是否已存在
        counter = 1
        original_username = username
        while User.query.filter_by(username=username).first():
            username = f"{original_username}{counter}"
            counter += 1
        
        user = User(
            username=username,
            email=email,
            password_hash=bcrypt.generate_password_hash(password).decode('utf-8')
        )
        
        db.session.add(user)
        db.session.commit()
        
        return {"success": True, "message": "注册成功", "user": user.to_dict()}
    
    @staticmethod
    def register_with_phone(phone, password, username=None):
        """手机号注册"""
        if not AuthService.validate_phone(phone):
            return {"success": False, "message": "手机号格式不正确"}
        
        if User.query.filter_by(phone=phone).first():
            return {"success": False, "message": "手机号已被注册"}
        
        if not username:
            username = f"user_{phone[-4:]}"
        
        # 检查用户名是否已存在
        counter = 1
        original_username = username
        while User.query.filter_by(username=username).first():
            username = f"{original_username}{counter}"
            counter += 1
        
        user = User(
            username=username,
            phone=phone,
            password_hash=bcrypt.generate_password_hash(password).decode('utf-8')
        )
        
        db.session.add(user)
        db.session.commit()
        
        return {"success": True, "message": "注册成功", "user": user.to_dict()}
    
    @staticmethod
    def login_with_email(email, password):
        """邮箱登录"""
        user = User.query.filter_by(email=email).first()
        if not user or not bcrypt.check_password_hash(user.password_hash, password):
            return {"success": False, "message": "邮箱或密码错误"}
        
        if not user.is_active:
            return {"success": False, "message": "账户已被禁用"}
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # 生成JWT令牌
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        return {
            "success": True,
            "message": "登录成功",
            "user": user.to_dict(),
            "access_token": access_token,
            "refresh_token": refresh_token
        }
    
    @staticmethod
    def login_with_phone(phone, password):
        """手机号登录"""
        user = User.query.filter_by(phone=phone).first()
        if not user or not bcrypt.check_password_hash(user.password_hash, password):
            return {"success": False, "message": "手机号或密码错误"}
        
        if not user.is_active:
            return {"success": False, "message": "账户已被禁用"}
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # 生成JWT令牌
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        return {
            "success": True,
            "message": "登录成功",
            "user": user.to_dict(),
            "access_token": access_token,
            "refresh_token": refresh_token
        }
    
    @staticmethod
    def login_with_verification_code(phone, code):
        """验证码登录"""
        if not AuthService.verify_code(phone, code):
            return {"success": False, "message": "验证码错误或已过期"}
        
        user = User.query.filter_by(phone=phone).first()
        if not user:
            # 如果用户不存在，自动注册
            username = f"user_{phone[-4:]}"
            counter = 1
            original_username = username
            while User.query.filter_by(username=username).first():
                username = f"{original_username}{counter}"
                counter += 1
            
            user = User(
                username=username,
                phone=phone,
                phone_verified=True
            )
            db.session.add(user)
        
        if not user.is_active:
            return {"success": False, "message": "账户已被禁用"}
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # 生成JWT令牌
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        return {
            "success": True,
            "message": "登录成功",
            "user": user.to_dict(),
            "access_token": access_token,
            "refresh_token": refresh_token
        }
    
    @staticmethod
    def wechat_login(code):
        """微信登录"""
        try:
            # 获取微信access_token
            token_url = "https://api.weixin.qq.com/sns/oauth2/access_token"
            token_params = {
                "appid": current_app.config['WECHAT_APP_ID'],
                "secret": current_app.config['WECHAT_APP_SECRET'],
                "code": code,
                "grant_type": "authorization_code"
            }
            
            response = requests.get(token_url, params=token_params)
            token_data = response.json()
            
            if "errcode" in token_data:
                return {"success": False, "message": "微信登录失败"}
            
            access_token = token_data["access_token"]
            openid = token_data["openid"]
            
            # 获取用户信息
            user_info_url = "https://api.weixin.qq.com/sns/userinfo"
            user_params = {
                "access_token": access_token,
                "openid": openid,
                "lang": "zh_CN"
            }
            
            response = requests.get(user_info_url, params=user_params)
            user_data = response.json()
            
            # 查找或创建用户
            user = User.query.filter_by(wechat_openid=openid).first()
            if not user:
                username = f"wx_{openid[-8:]}"
                counter = 1
                original_username = username
                while User.query.filter_by(username=username).first():
                    username = f"{original_username}{counter}"
                    counter += 1
                
                user = User(
                    username=username,
                    wechat_openid=openid,
                    nickname=user_data.get("nickname"),
                    avatar=user_data.get("headimgurl"),
                    is_verified=True
                )
                db.session.add(user)
            
            # 更新最后登录时间
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            # 生成JWT令牌
            access_token = create_access_token(identity=user.id)
            refresh_token = create_refresh_token(identity=user.id)
            
            return {
                "success": True,
                "message": "微信登录成功",
                "user": user.to_dict(),
                "access_token": access_token,
                "refresh_token": refresh_token
            }
            
        except Exception as e:
            print(f"微信登录失败: {e}")
            return {"success": False, "message": "微信登录失败"}
    
    @staticmethod
    def alipay_login(auth_code):
        """支付宝登录"""
        try:
            # 这里需要集成支付宝开放平台API
            # 实际实现需要添加签名等认证信息
            print(f"支付宝登录: {auth_code}")
            
            # 模拟支付宝登录成功
            user = User.query.filter_by(alipay_user_id=auth_code).first()
            if not user:
                username = f"alipay_{auth_code[-8:]}"
                counter = 1
                original_username = username
                while User.query.filter_by(username=username).first():
                    username = f"{original_username}{counter}"
                    counter += 1
                
                user = User(
                    username=username,
                    alipay_user_id=auth_code,
                    is_verified=True
                )
                db.session.add(user)
            
            # 更新最后登录时间
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            # 生成JWT令牌
            access_token = create_access_token(identity=user.id)
            refresh_token = create_refresh_token(identity=user.id)
            
            return {
                "success": True,
                "message": "支付宝登录成功",
                "user": user.to_dict(),
                "access_token": access_token,
                "refresh_token": refresh_token
            }
            
        except Exception as e:
            print(f"支付宝登录失败: {e}")
            return {"success": False, "message": "支付宝登录失败"} 