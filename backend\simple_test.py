#!/usr/bin/env python3
"""
简单的Flask测试应用
"""

from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/')
def hello():
    return jsonify({"message": "Hello World", "status": "running"})

@app.route('/test')
def test():
    return jsonify({"test": "success"})

if __name__ == '__main__':
    try:
        print("启动简单测试应用...")
        app.run(host='127.0.0.1', port=5002, debug=False)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
