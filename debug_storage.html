<!DOCTYPE html>
<html>
<head>
    <title>调试本地存储</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
        .user-item { margin: 10px 0; padding: 10px; background: #f9f9f9; border-left: 3px solid #007bff; }
    </style>
</head>
<body>
    <h1>本地存储调试工具</h1>
    
    <div class="section">
        <h2>当前存储的用户</h2>
        <button onclick="showUsers()">显示用户列表</button>
        <button onclick="clearUsers()">清空用户数据</button>
        <div id="usersList"></div>
    </div>
    
    <div class="section">
        <h2>当前登录状态</h2>
        <button onclick="showLoginStatus()">显示登录状态</button>
        <button onclick="clearLoginData()">清空登录数据</button>
        <div id="loginStatus"></div>
    </div>
    
    <div class="section">
        <h2>测试手机号登录</h2>
        <input type="text" id="testPhone" placeholder="手机号" value="13800138000">
        <input type="password" id="testPassword" placeholder="密码" value="123456">
        <button onclick="testPhoneLogin()">测试登录</button>
        <div id="loginResult"></div>
    </div>

    <script>
        function showUsers() {
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const usersList = document.getElementById('usersList');
            
            if (users.length === 0) {
                usersList.innerHTML = '<p>没有注册用户</p>';
                return;
            }
            
            let html = '<h3>注册用户列表：</h3>';
            users.forEach((user, index) => {
                html += `
                    <div class="user-item">
                        <strong>用户 ${index + 1}:</strong><br>
                        ID: ${user.id}<br>
                        用户名: ${user.username}<br>
                        邮箱: ${user.email || '无'}<br>
                        手机号: ${user.phone || '无'}<br>
                        密码: ${user.password}<br>
                        创建时间: ${user.createdAt}
                    </div>
                `;
            });
            usersList.innerHTML = html;
        }
        
        function clearUsers() {
            localStorage.removeItem('users');
            document.getElementById('usersList').innerHTML = '<p>用户数据已清空</p>';
        }
        
        function showLoginStatus() {
            const token = localStorage.getItem('access_token');
            const user = localStorage.getItem('user');
            const refreshToken = localStorage.getItem('refresh_token');
            
            const loginStatus = document.getElementById('loginStatus');
            
            let html = '<h3>当前登录状态：</h3>';
            html += `<p><strong>Access Token:</strong> ${token || '无'}</p>`;
            html += `<p><strong>Refresh Token:</strong> ${refreshToken || '无'}</p>`;
            
            if (user) {
                try {
                    const userData = JSON.parse(user);
                    html += '<p><strong>用户信息:</strong></p>';
                    html += `<pre>${JSON.stringify(userData, null, 2)}</pre>`;
                } catch (e) {
                    html += '<p><strong>用户信息:</strong> 解析失败</p>';
                }
            } else {
                html += '<p><strong>用户信息:</strong> 无</p>';
            }
            
            loginStatus.innerHTML = html;
        }
        
        function clearLoginData() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user');
            document.getElementById('loginStatus').innerHTML = '<p>登录数据已清空</p>';
        }
        
        async function testPhoneLogin() {
            const phone = document.getElementById('testPhone').value;
            const password = document.getElementById('testPassword').value;
            const resultDiv = document.getElementById('loginResult');
            
            resultDiv.innerHTML = '测试中...';
            
            try {
                // 模拟登录逻辑
                const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');
                const user = existingUsers.find(u => u.phone === phone && u.password === password);
                
                if (user) {
                    const loginResponse = {
                        success: true,
                        message: '登录成功',
                        data: {
                            user: {
                                id: user.id,
                                username: user.username,
                                email: user.email,
                                phone: user.phone,
                                avatar: user.avatar
                            },
                            token: `mock_token_${user.id}_${Date.now()}`
                        }
                    };
                    
                    resultDiv.innerHTML = `
                        <h4>✅ 登录成功</h4>
                        <pre>${JSON.stringify(loginResponse, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h4>❌ 登录失败</h4>
                        <p>手机号或密码错误</p>
                        <p>查找的手机号: ${phone}</p>
                        <p>查找的密码: ${password}</p>
                        <p>现有用户数量: ${existingUsers.length}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h4>❌ 测试失败</h4>
                    <p>${error.message}</p>
                `;
            }
        }
        
        // 页面加载时显示当前状态
        window.onload = function() {
            showUsers();
            showLoginStatus();
        };
    </script>
</body>
</html>
