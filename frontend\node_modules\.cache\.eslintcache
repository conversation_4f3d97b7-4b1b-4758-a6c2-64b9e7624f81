[{"D:\\work\\python_work\\Family_Takeout\\frontend\\src\\index.tsx": "1", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\App.tsx": "2", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\reportWebVitals.ts": "3", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\OrderPage.tsx": "4", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\LoginPage.tsx": "5", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\AddressPage.tsx": "6", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\RegisterPage.tsx": "7", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\DashboardPage.tsx": "8", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\ProfilePage.tsx": "9", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\components\\PrivateRoute.tsx": "10", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\AuthContext.tsx": "11", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\services\\authService.ts": "12", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\ProductsPage.tsx": "13", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\CartContext.tsx": "14", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\CartPage.tsx": "15", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\CheckoutPage.tsx": "16", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\PaymentPage.tsx": "17", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\AddressContext.tsx": "18", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\MerchantRefundPage.tsx": "19", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\OrderContext.tsx": "20", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\ProductDetailPage.tsx": "21", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\data\\products.ts": "22", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\FavoriteContext.tsx": "23", "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\FavoritePage.tsx": "24"}, {"size": 571, "mtime": 1752225583454, "results": "25", "hashOfConfig": "26"}, {"size": 3870, "mtime": 1752242527426, "results": "27", "hashOfConfig": "26"}, {"size": 438, "mtime": 1752216623782, "results": "28", "hashOfConfig": "26"}, {"size": 16828, "mtime": 1752239628045, "results": "29", "hashOfConfig": "26"}, {"size": 8949, "mtime": 1752210411523, "results": "30", "hashOfConfig": "26"}, {"size": 9075, "mtime": 1752232775775, "results": "31", "hashOfConfig": "26"}, {"size": 10047, "mtime": 1752210447310, "results": "32", "hashOfConfig": "26"}, {"size": 12390, "mtime": 1752242579542, "results": "33", "hashOfConfig": "26"}, {"size": 8191, "mtime": 1752210507134, "results": "34", "hashOfConfig": "26"}, {"size": 603, "mtime": 1752210380391, "results": "35", "hashOfConfig": "26"}, {"size": 4516, "mtime": 1752210336273, "results": "36", "hashOfConfig": "26"}, {"size": 9787, "mtime": 1752297888827, "results": "37", "hashOfConfig": "26"}, {"size": 11205, "mtime": 1752232929706, "results": "38", "hashOfConfig": "26"}, {"size": 7492, "mtime": 1752234085721, "results": "39", "hashOfConfig": "26"}, {"size": 10846, "mtime": 1752228123167, "results": "40", "hashOfConfig": "26"}, {"size": 17053, "mtime": 1752237845535, "results": "41", "hashOfConfig": "26"}, {"size": 9793, "mtime": 1752237861584, "results": "42", "hashOfConfig": "26"}, {"size": 8032, "mtime": 1752232845365, "results": "43", "hashOfConfig": "26"}, {"size": 8647, "mtime": 1752239711330, "results": "44", "hashOfConfig": "26"}, {"size": 8074, "mtime": 1752238099590, "results": "45", "hashOfConfig": "26"}, {"size": 13087, "mtime": 1752242423674, "results": "46", "hashOfConfig": "26"}, {"size": 8346, "mtime": 1752242600086, "results": "47", "hashOfConfig": "26"}, {"size": 4906, "mtime": 1752242185422, "results": "48", "hashOfConfig": "26"}, {"size": 7635, "mtime": 1752242473250, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c00xlo", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\index.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\App.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\OrderPage.tsx", ["122", "123", "124"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\LoginPage.tsx", ["125"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\AddressPage.tsx", ["126", "127"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\RegisterPage.tsx", ["128", "129", "130"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\DashboardPage.tsx", ["131", "132", "133", "134", "135"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\ProfilePage.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\components\\PrivateRoute.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\services\\authService.ts", ["136", "137"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\ProductsPage.tsx", ["138"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\CartContext.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\CartPage.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\CheckoutPage.tsx", ["139", "140", "141", "142", "143"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\PaymentPage.tsx", ["144", "145", "146"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\AddressContext.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\MerchantRefundPage.tsx", ["147", "148"], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\OrderContext.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\ProductDetailPage.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\data\\products.ts", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\contexts\\FavoriteContext.tsx", [], [], "D:\\work\\python_work\\Family_Takeout\\frontend\\src\\pages\\FavoritePage.tsx", [], [], {"ruleId": "149", "severity": 1, "message": "150", "line": 1, "column": 27, "nodeType": "151", "messageId": "152", "endLine": 1, "endColumn": 36}, {"ruleId": "149", "severity": 1, "message": "153", "line": 15, "column": 11, "nodeType": "151", "messageId": "152", "endLine": 15, "endColumn": 15}, {"ruleId": "149", "severity": 1, "message": "154", "line": 19, "column": 19, "nodeType": "151", "messageId": "152", "endLine": 19, "endColumn": 29}, {"ruleId": "149", "severity": 1, "message": "155", "line": 3, "column": 10, "nodeType": "151", "messageId": "152", "endLine": 3, "endColumn": 22}, {"ruleId": "149", "severity": 1, "message": "150", "line": 1, "column": 27, "nodeType": "151", "messageId": "152", "endLine": 1, "endColumn": 36}, {"ruleId": "149", "severity": 1, "message": "153", "line": 10, "column": 11, "nodeType": "151", "messageId": "152", "endLine": 10, "endColumn": 15}, {"ruleId": "149", "severity": 1, "message": "156", "line": 13, "column": 10, "nodeType": "151", "messageId": "152", "endLine": 13, "endColumn": 19}, {"ruleId": "149", "severity": 1, "message": "157", "line": 45, "column": 9, "nodeType": "151", "messageId": "152", "endLine": 45, "endColumn": 28}, {"ruleId": "149", "severity": 1, "message": "158", "line": 74, "column": 9, "nodeType": "151", "messageId": "152", "endLine": 74, "endColumn": 26}, {"ruleId": "149", "severity": 1, "message": "159", "line": 9, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 9, "endColumn": 21}, {"ruleId": "149", "severity": 1, "message": "160", "line": 15, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 15, "endColumn": 19}, {"ruleId": "149", "severity": 1, "message": "161", "line": 18, "column": 23, "nodeType": "151", "messageId": "152", "endLine": 18, "endColumn": 29}, {"ruleId": "149", "severity": 1, "message": "162", "line": 21, "column": 23, "nodeType": "151", "messageId": "152", "endLine": 21, "endColumn": 30}, {"ruleId": "149", "severity": 1, "message": "163", "line": 92, "column": 10, "nodeType": "151", "messageId": "152", "endLine": 92, "endColumn": 26}, {"ruleId": "164", "severity": 1, "message": "165", "line": 285, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 285, "endColumn": 24}, {"ruleId": "164", "severity": 1, "message": "168", "line": 293, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 293, "endColumn": 22}, {"ruleId": "169", "severity": 1, "message": "170", "line": 81, "column": 6, "nodeType": "171", "endLine": 81, "endColumn": 18, "suggestions": "172"}, {"ruleId": "149", "severity": 1, "message": "173", "line": 10, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 10, "endColumn": 9}, {"ruleId": "149", "severity": 1, "message": "174", "line": 20, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 20, "endColumn": 8}, {"ruleId": "149", "severity": 1, "message": "175", "line": 41, "column": 7, "nodeType": "151", "messageId": "152", "endLine": 41, "endColumn": 31}, {"ruleId": "149", "severity": 1, "message": "176", "line": 62, "column": 9, "nodeType": "151", "messageId": "152", "endLine": 62, "endColumn": 18}, {"ruleId": "149", "severity": 1, "message": "177", "line": 75, "column": 9, "nodeType": "151", "messageId": "152", "endLine": 75, "endColumn": 28}, {"ruleId": "149", "severity": 1, "message": "178", "line": 19, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 19, "endColumn": 22}, {"ruleId": "149", "severity": 1, "message": "179", "line": 20, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 20, "endColumn": 22}, {"ruleId": "149", "severity": 1, "message": "180", "line": 42, "column": 11, "nodeType": "151", "messageId": "152", "endLine": 42, "endColumn": 20}, {"ruleId": "149", "severity": 1, "message": "181", "line": 18, "column": 11, "nodeType": "151", "messageId": "152", "endLine": 18, "endColumn": 17}, {"ruleId": "149", "severity": 1, "message": "154", "line": 20, "column": 19, "nodeType": "151", "messageId": "152", "endLine": 20, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'UserOutlined' is defined but never used.", "'countdown' is assigned a value but never used.", "'handleSendEmailCode' is assigned a value but never used.", "'handleSendSmsCode' is assigned a value but never used.", "'UserSwitchOutlined' is defined but never used.", "'ShoppingOutlined' is defined but never used.", "'Outlet' is defined but never used.", "'Product' is defined but never used.", "'filteredProducts' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'sendEmailVerification'.", "ObjectExpression", "unexpected", "Duplicate key 'sendSmsVerification'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mockProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["182"], "'Select' is defined but never used.", "'Modal' is defined but never used.", "'convertAddressToDelivery' is assigned a value but never used.", "'addresses' is assigned a value but never used.", "'handleAddressSelect' is assigned a value but never used.", "'CheckCircleOutlined' is defined but never used.", "'CloseCircleOutlined' is defined but never used.", "'clearCart' is assigned a value but never used.", "'orders' is assigned a value but never used.", {"desc": "183", "fix": "184"}, "Update the dependencies array to be: [categoryId, mockProducts]", {"range": "185", "text": "186"}, [3483, 3495], "[categoryId, mockProducts]"]