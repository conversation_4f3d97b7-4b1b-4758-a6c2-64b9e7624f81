{"ast": null, "code": "var _jsxFileName = \"D:\\\\work\\\\python_work\\\\Family_Takeout\\\\frontend\\\\src\\\\pages\\\\RegisterPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Tabs, message, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, WechatOutlined, AlipayOutlined } from '@ant-design/icons';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst RegisterPage = () => {\n  _s();\n  const [emailForm] = Form.useForm();\n  const [phoneForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const {\n    register\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleEmailRegister = async values => {\n    try {\n      setLoading(true);\n      const success = await register('email', values);\n      if (success) {\n        navigate('/login');\n      }\n    } catch (error) {\n      message.error('注册失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePhoneRegister = async values => {\n    try {\n      setLoading(true);\n      const success = await register('phone', values);\n      if (success) {\n        navigate('/login');\n      }\n    } catch (error) {\n      message.error('注册失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSendEmailCode = async () => {\n    try {\n      const email = form.getFieldValue('email');\n      if (!email) {\n        message.error('请输入邮箱');\n        return;\n      }\n      const response = await authService.sendEmailVerification(email);\n      if (response.success) {\n        message.success('验证码已发送到邮箱');\n        setCountdown(60);\n        const timer = setInterval(() => {\n          setCountdown(prev => {\n            if (prev <= 1) {\n              clearInterval(timer);\n              return 0;\n            }\n            return prev - 1;\n          });\n        }, 1000);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error('发送验证码失败');\n    }\n  };\n  const handleSendSmsCode = async () => {\n    try {\n      const phone = form.getFieldValue('phone');\n      if (!phone) {\n        message.error('请输入手机号');\n        return;\n      }\n      const response = await authService.sendSmsVerification(phone);\n      if (response.success) {\n        message.success('验证码已发送');\n        setCountdown(60);\n        const timer = setInterval(() => {\n          setCountdown(prev => {\n            if (prev <= 1) {\n              clearInterval(timer);\n              return 0;\n            }\n            return prev - 1;\n          });\n        }, 1000);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error('发送验证码失败');\n    }\n  };\n  const handleWechatRegister = () => {\n    // 这里需要集成微信登录SDK\n    message.info('微信注册功能开发中...');\n  };\n  const handleAlipayRegister = () => {\n    // 这里需要集成支付宝登录SDK\n    message.info('支付宝注册功能开发中...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\u521B\\u5EFA\\u8D26\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u52A0\\u5165\\u5BB6\\u5EAD\\u5916\\u5356\\u7CFB\\u7EDF\\uFF0C\\u4EAB\\u53D7\\u4FBF\\u6377\\u670D\\u52A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        defaultActiveKey: \"email\",\n        centered: true,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u90AE\\u7BB1\\u6CE8\\u518C\",\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            name: \"email_register\",\n            onFinish: handleEmailRegister,\n            autoComplete: \"off\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 2,\n                message: '用户名长度不能少于2位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"email\",\n              rules: [{\n                required: true,\n                message: '请输入邮箱'\n              }, {\n                type: 'email',\n                message: '请输入有效的邮箱地址'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                message: '密码长度不能少于6位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"confirmPassword\",\n              dependencies: ['password'],\n              rules: [{\n                required: true,\n                message: '请确认密码'\n              }, ({\n                getFieldValue\n              }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('password') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                }\n              })],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u786E\\u8BA4\\u5BC6\\u7801\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                size: \"large\",\n                loading: loading,\n                className: \"submit-btn\",\n                children: \"\\u6CE8\\u518C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, \"email\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u624B\\u673A\\u53F7\\u6CE8\\u518C\",\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            name: \"phone_register\",\n            onFinish: handlePhoneRegister,\n            autoComplete: \"off\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 2,\n                message: '用户名长度不能少于2位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"phone\",\n              rules: [{\n                required: true,\n                message: '请输入手机号'\n              }, {\n                pattern: /^1[3-9]\\d{9}$/,\n                message: '请输入有效的手机号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(PhoneOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u624B\\u673A\\u53F7\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                message: '密码长度不能少于6位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"confirmPassword\",\n              dependencies: ['password'],\n              rules: [{\n                required: true,\n                message: '请确认密码'\n              }, ({\n                getFieldValue\n              }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('password') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                }\n              })],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u786E\\u8BA4\\u5BC6\\u7801\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                size: \"large\",\n                loading: loading,\n                className: \"submit-btn\",\n                children: \"\\u6CE8\\u518C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)\n        }, \"phone\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u6216\\u4F7F\\u7528\\u7B2C\\u4E09\\u65B9\\u6CE8\\u518C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"social-login-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(WechatOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 19\n          }, this),\n          className: \"wechat-btn\",\n          size: \"large\",\n          onClick: handleWechatRegister,\n          children: \"\\u5FAE\\u4FE1\\u6CE8\\u518C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(AlipayOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 19\n          }, this),\n          className: \"alipay-btn\",\n          size: \"large\",\n          onClick: handleAlipayRegister,\n          children: \"\\u652F\\u4ED8\\u5B9D\\u6CE8\\u518C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"switch-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u5DF2\\u6709\\u8D26\\u6237\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          children: \"\\u7ACB\\u5373\\u767B\\u5F55\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"7iPXGeAieYOooMPS8vOdBYkUcDQ=\", false, function () {\n  return [Form.useForm, Form.useForm, useAuth, useNavigate];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Tabs", "message", "Divider", "UserOutlined", "LockOutlined", "MailOutlined", "PhoneOutlined", "WechatOutlined", "AlipayOutlined", "Link", "useNavigate", "useAuth", "authService", "jsxDEV", "_jsxDEV", "TabPane", "RegisterPage", "_s", "emailForm", "useForm", "phoneForm", "loading", "setLoading", "countdown", "setCountdown", "register", "navigate", "handleEmailRegister", "values", "success", "error", "handlePhoneRegister", "handleSendEmailCode", "email", "form", "getFieldValue", "response", "sendEmailVerification", "timer", "setInterval", "prev", "clearInterval", "handleSendSmsCode", "phone", "sendSmsVerification", "handleWechatRegister", "info", "handleAlipayRegister", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultActiveKey", "centered", "tab", "name", "onFinish", "autoComplete", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "size", "type", "Password", "dependencies", "validator", "_", "value", "Promise", "resolve", "reject", "Error", "htmlType", "pattern", "icon", "onClick", "to", "_c", "$RefreshReg$"], "sources": ["D:/work/python_work/Family_Takeout/frontend/src/pages/RegisterPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Form, Input, Button, Tabs, message, Divider } from 'antd';\r\nimport { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, WechatOutlined, AlipayOutlined } from '@ant-design/icons';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../contexts/AuthContext';\r\nimport { authService } from '../services/authService';\r\n\r\nconst { TabPane } = Tabs;\r\n\r\nconst RegisterPage: React.FC = () => {\r\n  const [emailForm] = Form.useForm();\r\n  const [phoneForm] = Form.useForm();\r\n  const [loading, setLoading] = useState(false);\r\n  const [countdown, setCountdown] = useState(0);\r\n  const { register } = useAuth();\r\n  const navigate = useNavigate();\r\n\r\n  const handleEmailRegister = async (values: any) => {\r\n    try {\r\n      setLoading(true);\r\n      const success = await register('email', values);\r\n      if (success) {\r\n        navigate('/login');\r\n      }\r\n    } catch (error) {\r\n      message.error('注册失败，请稍后重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePhoneRegister = async (values: any) => {\r\n    try {\r\n      setLoading(true);\r\n      const success = await register('phone', values);\r\n      if (success) {\r\n        navigate('/login');\r\n      }\r\n    } catch (error) {\r\n      message.error('注册失败，请稍后重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSendEmailCode = async () => {\r\n    try {\r\n      const email = form.getFieldValue('email');\r\n      if (!email) {\r\n        message.error('请输入邮箱');\r\n        return;\r\n      }\r\n\r\n      const response = await authService.sendEmailVerification(email);\r\n      if (response.success) {\r\n        message.success('验证码已发送到邮箱');\r\n        setCountdown(60);\r\n        const timer = setInterval(() => {\r\n          setCountdown((prev) => {\r\n            if (prev <= 1) {\r\n              clearInterval(timer);\r\n              return 0;\r\n            }\r\n            return prev - 1;\r\n          });\r\n        }, 1000);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error('发送验证码失败');\r\n    }\r\n  };\r\n\r\n  const handleSendSmsCode = async () => {\r\n    try {\r\n      const phone = form.getFieldValue('phone');\r\n      if (!phone) {\r\n        message.error('请输入手机号');\r\n        return;\r\n      }\r\n\r\n      const response = await authService.sendSmsVerification(phone);\r\n      if (response.success) {\r\n        message.success('验证码已发送');\r\n        setCountdown(60);\r\n        const timer = setInterval(() => {\r\n          setCountdown((prev) => {\r\n            if (prev <= 1) {\r\n              clearInterval(timer);\r\n              return 0;\r\n            }\r\n            return prev - 1;\r\n          });\r\n        }, 1000);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error('发送验证码失败');\r\n    }\r\n  };\r\n\r\n  const handleWechatRegister = () => {\r\n    // 这里需要集成微信登录SDK\r\n    message.info('微信注册功能开发中...');\r\n  };\r\n\r\n  const handleAlipayRegister = () => {\r\n    // 这里需要集成支付宝登录SDK\r\n    message.info('支付宝注册功能开发中...');\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card\">\r\n        <div className=\"auth-header\">\r\n          <h1>创建账户</h1>\r\n          <p>加入家庭外卖系统，享受便捷服务</p>\r\n        </div>\r\n\r\n        <Tabs defaultActiveKey=\"email\" centered>\r\n          <TabPane tab=\"邮箱注册\" key=\"email\">\r\n            <Form\r\n              form={form}\r\n              name=\"email_register\"\r\n              onFinish={handleEmailRegister}\r\n              autoComplete=\"off\"\r\n            >\r\n              <Form.Item\r\n                name=\"username\"\r\n                rules={[\r\n                  { required: true, message: '请输入用户名' },\r\n                  { min: 2, message: '用户名长度不能少于2位' }\r\n                ]}\r\n              >\r\n                <Input\r\n                  prefix={<UserOutlined />}\r\n                  placeholder=\"请输入用户名\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"email\"\r\n                rules={[\r\n                  { required: true, message: '请输入邮箱' },\r\n                  { type: 'email', message: '请输入有效的邮箱地址' }\r\n                ]}\r\n              >\r\n                <Input\r\n                  prefix={<MailOutlined />}\r\n                  placeholder=\"请输入邮箱\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                rules={[\r\n                  { required: true, message: '请输入密码' },\r\n                  { min: 6, message: '密码长度不能少于6位' }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  prefix={<LockOutlined />}\r\n                  placeholder=\"请输入密码\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: '请确认密码' },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('两次输入的密码不一致'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  prefix={<LockOutlined />}\r\n                  placeholder=\"请确认密码\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <Button\r\n                  type=\"primary\"\r\n                  htmlType=\"submit\"\r\n                  size=\"large\"\r\n                  loading={loading}\r\n                  className=\"submit-btn\"\r\n                >\r\n                  注册\r\n                </Button>\r\n              </Form.Item>\r\n            </Form>\r\n          </TabPane>\r\n\r\n          <TabPane tab=\"手机号注册\" key=\"phone\">\r\n            <Form\r\n              form={form}\r\n              name=\"phone_register\"\r\n              onFinish={handlePhoneRegister}\r\n              autoComplete=\"off\"\r\n            >\r\n              <Form.Item\r\n                name=\"username\"\r\n                rules={[\r\n                  { required: true, message: '请输入用户名' },\r\n                  { min: 2, message: '用户名长度不能少于2位' }\r\n                ]}\r\n              >\r\n                <Input\r\n                  prefix={<UserOutlined />}\r\n                  placeholder=\"请输入用户名\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phone\"\r\n                rules={[\r\n                  { required: true, message: '请输入手机号' },\r\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号' }\r\n                ]}\r\n              >\r\n                <Input\r\n                  prefix={<PhoneOutlined />}\r\n                  placeholder=\"请输入手机号\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                rules={[\r\n                  { required: true, message: '请输入密码' },\r\n                  { min: 6, message: '密码长度不能少于6位' }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  prefix={<LockOutlined />}\r\n                  placeholder=\"请输入密码\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: '请确认密码' },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('两次输入的密码不一致'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  prefix={<LockOutlined />}\r\n                  placeholder=\"请确认密码\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <Button\r\n                  type=\"primary\"\r\n                  htmlType=\"submit\"\r\n                  size=\"large\"\r\n                  loading={loading}\r\n                  className=\"submit-btn\"\r\n                >\r\n                  注册\r\n                </Button>\r\n              </Form.Item>\r\n            </Form>\r\n          </TabPane>\r\n        </Tabs>\r\n\r\n        <Divider>\r\n          <span>或使用第三方注册</span>\r\n        </Divider>\r\n\r\n        <div className=\"social-login-buttons\">\r\n          <Button\r\n            icon={<WechatOutlined />}\r\n            className=\"wechat-btn\"\r\n            size=\"large\"\r\n            onClick={handleWechatRegister}\r\n          >\r\n            微信注册\r\n          </Button>\r\n          <Button\r\n            icon={<AlipayOutlined />}\r\n            className=\"alipay-btn\"\r\n            size=\"large\"\r\n            onClick={handleAlipayRegister}\r\n          >\r\n            支付宝注册\r\n          </Button>\r\n        </div>\r\n\r\n        <div className=\"switch-form\">\r\n          <span>已有账户？</span>\r\n          <Link to=\"/login\">立即登录</Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RegisterPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,OAAO,QAAQ,MAAM;AAClE,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAC3H,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAM;EAAEC;AAAQ,CAAC,GAAGf,IAAI;AAExB,MAAMgB,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,SAAS,CAAC,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;EAClC,MAAM,CAACC,SAAS,CAAC,GAAGvB,IAAI,CAACsB,OAAO,CAAC,CAAC;EAClC,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM;IAAE6B;EAAS,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,mBAAmB,GAAG,MAAOC,MAAW,IAAK;IACjD,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMO,OAAO,GAAG,MAAMJ,QAAQ,CAAC,OAAO,EAAEG,MAAM,CAAC;MAC/C,IAAIC,OAAO,EAAE;QACXH,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,mBAAmB,GAAG,MAAOH,MAAW,IAAK;IACjD,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMO,OAAO,GAAG,MAAMJ,QAAQ,CAAC,OAAO,EAAEG,MAAM,CAAC;MAC/C,IAAIC,OAAO,EAAE;QACXH,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,KAAK,GAAGC,IAAI,CAACC,aAAa,CAAC,OAAO,CAAC;MACzC,IAAI,CAACF,KAAK,EAAE;QACVhC,OAAO,CAAC6B,KAAK,CAAC,OAAO,CAAC;QACtB;MACF;MAEA,MAAMM,QAAQ,GAAG,MAAMxB,WAAW,CAACyB,qBAAqB,CAACJ,KAAK,CAAC;MAC/D,IAAIG,QAAQ,CAACP,OAAO,EAAE;QACpB5B,OAAO,CAAC4B,OAAO,CAAC,WAAW,CAAC;QAC5BL,YAAY,CAAC,EAAE,CAAC;QAChB,MAAMc,KAAK,GAAGC,WAAW,CAAC,MAAM;UAC9Bf,YAAY,CAAEgB,IAAI,IAAK;YACrB,IAAIA,IAAI,IAAI,CAAC,EAAE;cACbC,aAAa,CAACH,KAAK,CAAC;cACpB,OAAO,CAAC;YACV;YACA,OAAOE,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLvC,OAAO,CAAC6B,KAAK,CAACM,QAAQ,CAACnC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAAC,SAAS,CAAC;IAC1B;EACF,CAAC;EAED,MAAMY,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,KAAK,GAAGT,IAAI,CAACC,aAAa,CAAC,OAAO,CAAC;MACzC,IAAI,CAACQ,KAAK,EAAE;QACV1C,OAAO,CAAC6B,KAAK,CAAC,QAAQ,CAAC;QACvB;MACF;MAEA,MAAMM,QAAQ,GAAG,MAAMxB,WAAW,CAACgC,mBAAmB,CAACD,KAAK,CAAC;MAC7D,IAAIP,QAAQ,CAACP,OAAO,EAAE;QACpB5B,OAAO,CAAC4B,OAAO,CAAC,QAAQ,CAAC;QACzBL,YAAY,CAAC,EAAE,CAAC;QAChB,MAAMc,KAAK,GAAGC,WAAW,CAAC,MAAM;UAC9Bf,YAAY,CAAEgB,IAAI,IAAK;YACrB,IAAIA,IAAI,IAAI,CAAC,EAAE;cACbC,aAAa,CAACH,KAAK,CAAC;cACpB,OAAO,CAAC;YACV;YACA,OAAOE,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLvC,OAAO,CAAC6B,KAAK,CAACM,QAAQ,CAACnC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAAC,SAAS,CAAC;IAC1B;EACF,CAAC;EAED,MAAMe,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACA5C,OAAO,CAAC6C,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACA9C,OAAO,CAAC6C,IAAI,CAAC,eAAe,CAAC;EAC/B,CAAC;EAED,oBACEhC,OAAA;IAAKkC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BnC,OAAA;MAAKkC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBnC,OAAA;QAAKkC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnC,OAAA;UAAAmC,QAAA,EAAI;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACbvC,OAAA;UAAAmC,QAAA,EAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAENvC,OAAA,CAACd,IAAI;QAACsD,gBAAgB,EAAC,OAAO;QAACC,QAAQ;QAAAN,QAAA,gBACrCnC,OAAA,CAACC,OAAO;UAACyC,GAAG,EAAC,0BAAM;UAAAP,QAAA,eACjBnC,OAAA,CAACjB,IAAI;YACHqC,IAAI,EAAEA,IAAK;YACXuB,IAAI,EAAC,gBAAgB;YACrBC,QAAQ,EAAE/B,mBAAoB;YAC9BgC,YAAY,EAAC,KAAK;YAAAV,QAAA,gBAElBnC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7D,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE8D,GAAG,EAAE,CAAC;gBAAE9D,OAAO,EAAE;cAAc,CAAC,CAClC;cAAAgD,QAAA,eAEFnC,OAAA,CAAChB,KAAK;gBACJkE,MAAM,eAAElD,OAAA,CAACX,YAAY;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBY,WAAW,EAAC,sCAAQ;gBACpBC,IAAI,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZvC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cACRH,IAAI,EAAC,OAAO;cACZI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7D,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAEkE,IAAI,EAAE,OAAO;gBAAElE,OAAO,EAAE;cAAa,CAAC,CACxC;cAAAgD,QAAA,eAEFnC,OAAA,CAAChB,KAAK;gBACJkE,MAAM,eAAElD,OAAA,CAACT,YAAY;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBY,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZvC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7D,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAE8D,GAAG,EAAE,CAAC;gBAAE9D,OAAO,EAAE;cAAa,CAAC,CACjC;cAAAgD,QAAA,eAEFnC,OAAA,CAAChB,KAAK,CAACsE,QAAQ;gBACbJ,MAAM,eAAElD,OAAA,CAACV,YAAY;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBY,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZvC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cACRH,IAAI,EAAC,iBAAiB;cACtBY,YAAY,EAAE,CAAC,UAAU,CAAE;cAC3BR,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7D,OAAO,EAAE;cAAQ,CAAC,EACpC,CAAC;gBAAEkC;cAAc,CAAC,MAAM;gBACtBmC,SAASA,CAACC,CAAC,EAAEC,KAAK,EAAE;kBAClB,IAAI,CAACA,KAAK,IAAIrC,aAAa,CAAC,UAAU,CAAC,KAAKqC,KAAK,EAAE;oBACjD,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;kBAC1B;kBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChD;cACF,CAAC,CAAC,CACF;cAAA3B,QAAA,eAEFnC,OAAA,CAAChB,KAAK,CAACsE,QAAQ;gBACbJ,MAAM,eAAElD,OAAA,CAACV,YAAY;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBY,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZvC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cAAAX,QAAA,eACRnC,OAAA,CAACf,MAAM;gBACLoE,IAAI,EAAC,SAAS;gBACdU,QAAQ,EAAC,QAAQ;gBACjBX,IAAI,EAAC,OAAO;gBACZ7C,OAAO,EAAEA,OAAQ;gBACjB2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACvB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC,GAlFe,OAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmFtB,CAAC,eAEVvC,OAAA,CAACC,OAAO;UAACyC,GAAG,EAAC,gCAAO;UAAAP,QAAA,eAClBnC,OAAA,CAACjB,IAAI;YACHqC,IAAI,EAAEA,IAAK;YACXuB,IAAI,EAAC,gBAAgB;YACrBC,QAAQ,EAAE3B,mBAAoB;YAC9B4B,YAAY,EAAC,KAAK;YAAAV,QAAA,gBAElBnC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7D,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE8D,GAAG,EAAE,CAAC;gBAAE9D,OAAO,EAAE;cAAc,CAAC,CAClC;cAAAgD,QAAA,eAEFnC,OAAA,CAAChB,KAAK;gBACJkE,MAAM,eAAElD,OAAA,CAACX,YAAY;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBY,WAAW,EAAC,sCAAQ;gBACpBC,IAAI,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZvC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cACRH,IAAI,EAAC,OAAO;cACZI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7D,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE6E,OAAO,EAAE,eAAe;gBAAE7E,OAAO,EAAE;cAAY,CAAC,CAClD;cAAAgD,QAAA,eAEFnC,OAAA,CAAChB,KAAK;gBACJkE,MAAM,eAAElD,OAAA,CAACR,aAAa;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BY,WAAW,EAAC,sCAAQ;gBACpBC,IAAI,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZvC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7D,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAE8D,GAAG,EAAE,CAAC;gBAAE9D,OAAO,EAAE;cAAa,CAAC,CACjC;cAAAgD,QAAA,eAEFnC,OAAA,CAAChB,KAAK,CAACsE,QAAQ;gBACbJ,MAAM,eAAElD,OAAA,CAACV,YAAY;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBY,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZvC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cACRH,IAAI,EAAC,iBAAiB;cACtBY,YAAY,EAAE,CAAC,UAAU,CAAE;cAC3BR,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7D,OAAO,EAAE;cAAQ,CAAC,EACpC,CAAC;gBAAEkC;cAAc,CAAC,MAAM;gBACtBmC,SAASA,CAACC,CAAC,EAAEC,KAAK,EAAE;kBAClB,IAAI,CAACA,KAAK,IAAIrC,aAAa,CAAC,UAAU,CAAC,KAAKqC,KAAK,EAAE;oBACjD,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;kBAC1B;kBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChD;cACF,CAAC,CAAC,CACF;cAAA3B,QAAA,eAEFnC,OAAA,CAAChB,KAAK,CAACsE,QAAQ;gBACbJ,MAAM,eAAElD,OAAA,CAACV,YAAY;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBY,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZvC,OAAA,CAACjB,IAAI,CAAC+D,IAAI;cAAAX,QAAA,eACRnC,OAAA,CAACf,MAAM;gBACLoE,IAAI,EAAC,SAAS;gBACdU,QAAQ,EAAC,QAAQ;gBACjBX,IAAI,EAAC,OAAO;gBACZ7C,OAAO,EAAEA,OAAQ;gBACjB2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACvB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC,GAlFgB,OAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmFvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEPvC,OAAA,CAACZ,OAAO;QAAA+C,QAAA,eACNnC,OAAA;UAAAmC,QAAA,EAAM;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAEVvC,OAAA;QAAKkC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCnC,OAAA,CAACf,MAAM;UACLgF,IAAI,eAAEjE,OAAA,CAACP,cAAc;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBL,SAAS,EAAC,YAAY;UACtBkB,IAAI,EAAC,OAAO;UACZc,OAAO,EAAEnC,oBAAqB;UAAAI,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA,CAACf,MAAM;UACLgF,IAAI,eAAEjE,OAAA,CAACN,cAAc;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBL,SAAS,EAAC,YAAY;UACtBkB,IAAI,EAAC,OAAO;UACZc,OAAO,EAAEjC,oBAAqB;UAAAE,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvC,OAAA;QAAKkC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnC,OAAA;UAAAmC,QAAA,EAAM;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBvC,OAAA,CAACL,IAAI;UAACwE,EAAE,EAAC,QAAQ;UAAAhC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CA1TID,YAAsB;EAAA,QACNnB,IAAI,CAACsB,OAAO,EACZtB,IAAI,CAACsB,OAAO,EAGXR,OAAO,EACXD,WAAW;AAAA;AAAAwE,EAAA,GANxBlE,YAAsB;AA4T5B,eAAeA,YAAY;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}