{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000\n});\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('access_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理token过期\napi.interceptors.response.use(response => {\n  return response;\n}, async error => {\n  var _error$response;\n  const originalRequest = error.config;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n    originalRequest._retry = true;\n    const refreshToken = localStorage.getItem('refresh_token');\n    if (refreshToken) {\n      try {\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n          refresh_token: refreshToken\n        });\n        const {\n          access_token\n        } = response.data;\n        localStorage.setItem('access_token', access_token);\n        originalRequest.headers.Authorization = `Bearer ${access_token}`;\n        return api(originalRequest);\n      } catch (refreshError) {\n        // 刷新token失败，清除本地存储\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n  }\n  return Promise.reject(error);\n});\nexport const authService = {\n  // 邮箱注册\n  registerWithEmail: async (email, password, username) => {\n    try {\n      const response = await api.post('/auth/register/email', {\n        email,\n        password,\n        username\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response2;\n      if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && _error$response2.data) {\n        return error.response.data;\n      }\n      return {\n        success: false,\n        message: '注册失败，请稍后重试'\n      };\n    }\n  },\n  // 手机号注册\n  registerWithPhone: async (phone, password, username) => {\n    try {\n      const response = await api.post('/auth/register/phone', {\n        phone,\n        password,\n        username\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && _error$response3.data) {\n        return error.response.data;\n      }\n      return {\n        success: false,\n        message: '注册失败，请稍后重试'\n      };\n    }\n  },\n  // 邮箱登录\n  loginWithEmail: async (email, password) => {\n    try {\n      const response = await api.post('/auth/login/email', {\n        email,\n        password\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response4;\n      if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && _error$response4.data) {\n        return error.response.data;\n      }\n      return {\n        success: false,\n        message: '登录失败，请稍后重试'\n      };\n    }\n  },\n  // 手机号登录\n  loginWithPhone: async (phone, password) => {\n    try {\n      const response = await api.post('/auth/login/phone', {\n        phone,\n        password\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response5;\n      if ((_error$response5 = error.response) !== null && _error$response5 !== void 0 && _error$response5.data) {\n        return error.response.data;\n      }\n      return {\n        success: false,\n        message: '登录失败，请稍后重试'\n      };\n    }\n  },\n  // 验证码登录\n  loginWithVerificationCode: async (phone, code) => {\n    try {\n      const response = await api.post('/auth/login/verification-code', {\n        phone,\n        code\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response6;\n      if ((_error$response6 = error.response) !== null && _error$response6 !== void 0 && _error$response6.data) {\n        return error.response.data;\n      }\n      return {\n        success: false,\n        message: '登录失败，请稍后重试'\n      };\n    }\n  },\n  // 微信登录\n  loginWithWechat: async code => {\n    try {\n      const response = await api.post('/auth/login/wechat', {\n        code\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response7;\n      if ((_error$response7 = error.response) !== null && _error$response7 !== void 0 && _error$response7.data) {\n        return error.response.data;\n      }\n      return {\n        success: false,\n        message: '微信登录功能暂未开放'\n      };\n    }\n  },\n  // 支付宝登录\n  loginWithAlipay: async authCode => {\n    const response = await api.post('/auth/login/alipay', {\n      auth_code: authCode\n    });\n    return response.data;\n  },\n  // 发送邮箱验证码\n  sendEmailVerification: async email => {\n    try {\n      const response = await api.post('/auth/send-email-code', {\n        email\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response8;\n      if ((_error$response8 = error.response) !== null && _error$response8 !== void 0 && _error$response8.data) {\n        return error.response.data;\n      }\n      return {\n        success: false,\n        message: '发送验证码失败，请稍后重试'\n      };\n    }\n  },\n  // 发送短信验证码\n  sendSmsVerification: async phone => {\n    try {\n      const response = await api.post('/auth/send-sms-code', {\n        phone\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response9;\n      if ((_error$response9 = error.response) !== null && _error$response9 !== void 0 && _error$response9.data) {\n        return error.response.data;\n      }\n      return {\n        success: false,\n        message: '发送验证码失败，请稍后重试'\n      };\n    }\n  },\n  // 获取用户信息\n  getProfile: async () => {\n    const response = await api.get('/auth/profile');\n    return response.data.user;\n  },\n  // 登出\n  logout: async () => {\n    const response = await api.post('/auth/logout');\n    return response.data;\n  },\n  // 通用登录方法\n  login: async (type, credentials) => {\n    switch (type) {\n      case 'email':\n        return await authService.loginWithEmail(credentials.email, credentials.password);\n      case 'phone':\n        return await authService.loginWithPhone(credentials.phone, credentials.password);\n      case 'verification_code':\n        return await authService.loginWithVerificationCode(credentials.phone, credentials.code);\n      case 'wechat':\n        return await authService.loginWithWechat(credentials.code);\n      case 'alipay':\n        return await authService.loginWithAlipay(credentials.authCode);\n      default:\n        throw new Error('不支持的登录类型');\n    }\n  },\n  // 通用注册方法\n  register: async (type, credentials) => {\n    switch (type) {\n      case 'email':\n        return await authService.registerWithEmail(credentials.email, credentials.password, credentials.username);\n      case 'phone':\n        return await authService.registerWithPhone(credentials.phone, credentials.password, credentials.username);\n      default:\n        throw new Error('不支持的注册类型');\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "post", "refresh_token", "access_token", "data", "setItem", "refreshError", "removeItem", "window", "location", "href", "authService", "registerWithEmail", "email", "password", "username", "_error$response2", "success", "message", "registerWithPhone", "phone", "_error$response3", "loginWithEmail", "_error$response4", "loginWithPhone", "_error$response5", "loginWithVerificationCode", "code", "_error$response6", "loginWithWechat", "_error$response7", "loginWithAlipay", "authCode", "auth_code", "sendEmailVerification", "_error$response8", "sendSmsVerification", "_error$response9", "getProfile", "get", "user", "logout", "login", "type", "credentials", "Error", "register"], "sources": ["D:/work/python_work/Family_Takeout/frontend/src/services/authService.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\r\n\r\n// 创建axios实例\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 10000,\r\n});\r\n\r\n// 请求拦截器 - 添加token\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('access_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 响应拦截器 - 处理token过期\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      const refreshToken = localStorage.getItem('refresh_token');\r\n      if (refreshToken) {\r\n        try {\r\n          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\r\n            refresh_token: refreshToken,\r\n          });\r\n          \r\n          const { access_token } = response.data;\r\n          localStorage.setItem('access_token', access_token);\r\n          \r\n          originalRequest.headers.Authorization = `Bearer ${access_token}`;\r\n          return api(originalRequest);\r\n        } catch (refreshError) {\r\n          // 刷新token失败，清除本地存储\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          localStorage.removeItem('user');\r\n          window.location.href = '/login';\r\n          return Promise.reject(refreshError);\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport const authService = {\r\n  // 邮箱注册\r\n  registerWithEmail: async (email: string, password: string, username?: string) => {\r\n    try {\r\n      const response = await api.post('/auth/register/email', {\r\n        email,\r\n        password,\r\n        username\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.data) {\r\n        return error.response.data;\r\n      }\r\n      return {\r\n        success: false,\r\n        message: '注册失败，请稍后重试'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 手机号注册\r\n  registerWithPhone: async (phone: string, password: string, username?: string) => {\r\n    try {\r\n      const response = await api.post('/auth/register/phone', {\r\n        phone,\r\n        password,\r\n        username\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.data) {\r\n        return error.response.data;\r\n      }\r\n      return {\r\n        success: false,\r\n        message: '注册失败，请稍后重试'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 邮箱登录\r\n  loginWithEmail: async (email: string, password: string) => {\r\n    try {\r\n      const response = await api.post('/auth/login/email', {\r\n        email,\r\n        password\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.data) {\r\n        return error.response.data;\r\n      }\r\n      return {\r\n        success: false,\r\n        message: '登录失败，请稍后重试'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 手机号登录\r\n  loginWithPhone: async (phone: string, password: string) => {\r\n    try {\r\n      const response = await api.post('/auth/login/phone', {\r\n        phone,\r\n        password\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.data) {\r\n        return error.response.data;\r\n      }\r\n      return {\r\n        success: false,\r\n        message: '登录失败，请稍后重试'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 验证码登录\r\n  loginWithVerificationCode: async (phone: string, code: string) => {\r\n    try {\r\n      const response = await api.post('/auth/login/verification-code', {\r\n        phone,\r\n        code\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.data) {\r\n        return error.response.data;\r\n      }\r\n      return {\r\n        success: false,\r\n        message: '登录失败，请稍后重试'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 微信登录\r\n  loginWithWechat: async (code: string) => {\r\n    try {\r\n      const response = await api.post('/auth/login/wechat', {\r\n        code\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.data) {\r\n        return error.response.data;\r\n      }\r\n      return {\r\n        success: false,\r\n        message: '微信登录功能暂未开放'\r\n      };\r\n    }\r\n  },\r\n\r\n\r\n\r\n  // 支付宝登录\r\n  loginWithAlipay: async (authCode: string) => {\r\n    const response = await api.post('/auth/login/alipay', {\r\n      auth_code: authCode,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 发送邮箱验证码\r\n  sendEmailVerification: async (email: string) => {\r\n    try {\r\n      const response = await api.post('/auth/send-email-code', {\r\n        email,\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.data) {\r\n        return error.response.data;\r\n      }\r\n      return {\r\n        success: false,\r\n        message: '发送验证码失败，请稍后重试'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 发送短信验证码\r\n  sendSmsVerification: async (phone: string) => {\r\n    try {\r\n      const response = await api.post('/auth/send-sms-code', {\r\n        phone,\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.data) {\r\n        return error.response.data;\r\n      }\r\n      return {\r\n        success: false,\r\n        message: '发送验证码失败，请稍后重试'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 获取用户信息\r\n  getProfile: async () => {\r\n    const response = await api.get('/auth/profile');\r\n    return response.data.user;\r\n  },\r\n\r\n  // 登出\r\n  logout: async () => {\r\n    const response = await api.post('/auth/logout');\r\n    return response.data;\r\n  },\r\n\r\n  // 通用登录方法\r\n  login: async (type: string, credentials: any) => {\r\n    switch (type) {\r\n      case 'email':\r\n        return await authService.loginWithEmail(credentials.email, credentials.password);\r\n      case 'phone':\r\n        return await authService.loginWithPhone(credentials.phone, credentials.password);\r\n      case 'verification_code':\r\n        return await authService.loginWithVerificationCode(credentials.phone, credentials.code);\r\n      case 'wechat':\r\n        return await authService.loginWithWechat(credentials.code);\r\n      case 'alipay':\r\n        return await authService.loginWithAlipay(credentials.authCode);\r\n      default:\r\n        throw new Error('不支持的登录类型');\r\n    }\r\n  },\r\n\r\n  // 通用注册方法\r\n  register: async (type: string, credentials: any) => {\r\n    switch (type) {\r\n      case 'email':\r\n        return await authService.registerWithEmail(credentials.email, credentials.password, credentials.username);\r\n      case 'phone':\r\n        return await authService.registerWithPhone(credentials.phone, credentials.password, credentials.username);\r\n      default:\r\n        throw new Error('不支持的注册类型');\r\n    }\r\n  },\r\n}; "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACI,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACD,MAAOH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACf,MAAMC,eAAe,GAAGL,KAAK,CAACN,MAAM;EAEpC,IAAI,EAAAU,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;IAE7B,MAAMC,YAAY,GAAGZ,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC1D,IAAIW,YAAY,EAAE;MAChB,IAAI;QACF,MAAML,QAAQ,GAAG,MAAMrB,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,YAAY,eAAe,EAAE;UAChE2B,aAAa,EAAEF;QACjB,CAAC,CAAC;QAEF,MAAM;UAAEG;QAAa,CAAC,GAAGR,QAAQ,CAACS,IAAI;QACtChB,YAAY,CAACiB,OAAO,CAAC,cAAc,EAAEF,YAAY,CAAC;QAElDN,eAAe,CAACP,OAAO,CAACC,aAAa,GAAG,UAAUY,YAAY,EAAE;QAChE,OAAOxB,GAAG,CAACkB,eAAe,CAAC;MAC7B,CAAC,CAAC,OAAOS,YAAY,EAAE;QACrB;QACAlB,YAAY,CAACmB,UAAU,CAAC,cAAc,CAAC;QACvCnB,YAAY,CAACmB,UAAU,CAAC,eAAe,CAAC;QACxCnB,YAAY,CAACmB,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B,OAAOjB,OAAO,CAACC,MAAM,CAACY,YAAY,CAAC;MACrC;IACF;EACF;EAEA,OAAOb,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,OAAO,MAAMmB,WAAW,GAAG;EACzB;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,EAAEC,QAAiB,KAAK;IAC/E,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,sBAAsB,EAAE;QACtDY,KAAK;QACLC,QAAQ;QACRC;MACF,CAAC,CAAC;MACF,OAAOpB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAwB,gBAAA;MACnB,KAAAA,gBAAA,GAAIxB,KAAK,CAACG,QAAQ,cAAAqB,gBAAA,eAAdA,gBAAA,CAAgBZ,IAAI,EAAE;QACxB,OAAOZ,KAAK,CAACG,QAAQ,CAACS,IAAI;MAC5B;MACA,OAAO;QACLa,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,KAAa,EAAEN,QAAgB,EAAEC,QAAiB,KAAK;IAC/E,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,sBAAsB,EAAE;QACtDmB,KAAK;QACLN,QAAQ;QACRC;MACF,CAAC,CAAC;MACF,OAAOpB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAA6B,gBAAA;MACnB,KAAAA,gBAAA,GAAI7B,KAAK,CAACG,QAAQ,cAAA0B,gBAAA,eAAdA,gBAAA,CAAgBjB,IAAI,EAAE;QACxB,OAAOZ,KAAK,CAACG,QAAQ,CAACS,IAAI;MAC5B;MACA,OAAO;QACLa,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAI,cAAc,EAAE,MAAAA,CAAOT,KAAa,EAAEC,QAAgB,KAAK;IACzD,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,mBAAmB,EAAE;QACnDY,KAAK;QACLC;MACF,CAAC,CAAC;MACF,OAAOnB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAA+B,gBAAA;MACnB,KAAAA,gBAAA,GAAI/B,KAAK,CAACG,QAAQ,cAAA4B,gBAAA,eAAdA,gBAAA,CAAgBnB,IAAI,EAAE;QACxB,OAAOZ,KAAK,CAACG,QAAQ,CAACS,IAAI;MAC5B;MACA,OAAO;QACLa,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAM,cAAc,EAAE,MAAAA,CAAOJ,KAAa,EAAEN,QAAgB,KAAK;IACzD,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,mBAAmB,EAAE;QACnDmB,KAAK;QACLN;MACF,CAAC,CAAC;MACF,OAAOnB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAiC,gBAAA;MACnB,KAAAA,gBAAA,GAAIjC,KAAK,CAACG,QAAQ,cAAA8B,gBAAA,eAAdA,gBAAA,CAAgBrB,IAAI,EAAE;QACxB,OAAOZ,KAAK,CAACG,QAAQ,CAACS,IAAI;MAC5B;MACA,OAAO;QACLa,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAQ,yBAAyB,EAAE,MAAAA,CAAON,KAAa,EAAEO,IAAY,KAAK;IAChE,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,+BAA+B,EAAE;QAC/DmB,KAAK;QACLO;MACF,CAAC,CAAC;MACF,OAAOhC,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAoC,gBAAA;MACnB,KAAAA,gBAAA,GAAIpC,KAAK,CAACG,QAAQ,cAAAiC,gBAAA,eAAdA,gBAAA,CAAgBxB,IAAI,EAAE;QACxB,OAAOZ,KAAK,CAACG,QAAQ,CAACS,IAAI;MAC5B;MACA,OAAO;QACLa,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAW,eAAe,EAAE,MAAOF,IAAY,IAAK;IACvC,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,oBAAoB,EAAE;QACpD0B;MACF,CAAC,CAAC;MACF,OAAOhC,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAsC,gBAAA;MACnB,KAAAA,gBAAA,GAAItC,KAAK,CAACG,QAAQ,cAAAmC,gBAAA,eAAdA,gBAAA,CAAgB1B,IAAI,EAAE;QACxB,OAAOZ,KAAK,CAACG,QAAQ,CAACS,IAAI;MAC5B;MACA,OAAO;QACLa,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAID;EACAa,eAAe,EAAE,MAAOC,QAAgB,IAAK;IAC3C,MAAMrC,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,oBAAoB,EAAE;MACpDgC,SAAS,EAAED;IACb,CAAC,CAAC;IACF,OAAOrC,QAAQ,CAACS,IAAI;EACtB,CAAC;EAED;EACA8B,qBAAqB,EAAE,MAAOrB,KAAa,IAAK;IAC9C,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,uBAAuB,EAAE;QACvDY;MACF,CAAC,CAAC;MACF,OAAOlB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAA2C,gBAAA;MACnB,KAAAA,gBAAA,GAAI3C,KAAK,CAACG,QAAQ,cAAAwC,gBAAA,eAAdA,gBAAA,CAAgB/B,IAAI,EAAE;QACxB,OAAOZ,KAAK,CAACG,QAAQ,CAACS,IAAI;MAC5B;MACA,OAAO;QACLa,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAkB,mBAAmB,EAAE,MAAOhB,KAAa,IAAK;IAC5C,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,qBAAqB,EAAE;QACrDmB;MACF,CAAC,CAAC;MACF,OAAOzB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAA6C,gBAAA;MACnB,KAAAA,gBAAA,GAAI7C,KAAK,CAACG,QAAQ,cAAA0C,gBAAA,eAAdA,gBAAA,CAAgBjC,IAAI,EAAE;QACxB,OAAOZ,KAAK,CAACG,QAAQ,CAACS,IAAI;MAC5B;MACA,OAAO;QACLa,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAoB,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,MAAM3C,QAAQ,GAAG,MAAMhB,GAAG,CAAC4D,GAAG,CAAC,eAAe,CAAC;IAC/C,OAAO5C,QAAQ,CAACS,IAAI,CAACoC,IAAI;EAC3B,CAAC;EAED;EACAC,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,MAAM9C,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,cAAc,CAAC;IAC/C,OAAON,QAAQ,CAACS,IAAI;EACtB,CAAC;EAED;EACAsC,KAAK,EAAE,MAAAA,CAAOC,IAAY,EAAEC,WAAgB,KAAK;IAC/C,QAAQD,IAAI;MACV,KAAK,OAAO;QACV,OAAO,MAAMhC,WAAW,CAACW,cAAc,CAACsB,WAAW,CAAC/B,KAAK,EAAE+B,WAAW,CAAC9B,QAAQ,CAAC;MAClF,KAAK,OAAO;QACV,OAAO,MAAMH,WAAW,CAACa,cAAc,CAACoB,WAAW,CAACxB,KAAK,EAAEwB,WAAW,CAAC9B,QAAQ,CAAC;MAClF,KAAK,mBAAmB;QACtB,OAAO,MAAMH,WAAW,CAACe,yBAAyB,CAACkB,WAAW,CAACxB,KAAK,EAAEwB,WAAW,CAACjB,IAAI,CAAC;MACzF,KAAK,QAAQ;QACX,OAAO,MAAMhB,WAAW,CAACkB,eAAe,CAACe,WAAW,CAACjB,IAAI,CAAC;MAC5D,KAAK,QAAQ;QACX,OAAO,MAAMhB,WAAW,CAACoB,eAAe,CAACa,WAAW,CAACZ,QAAQ,CAAC;MAChE;QACE,MAAM,IAAIa,KAAK,CAAC,UAAU,CAAC;IAC/B;EACF,CAAC;EAED;EACAC,QAAQ,EAAE,MAAAA,CAAOH,IAAY,EAAEC,WAAgB,KAAK;IAClD,QAAQD,IAAI;MACV,KAAK,OAAO;QACV,OAAO,MAAMhC,WAAW,CAACC,iBAAiB,CAACgC,WAAW,CAAC/B,KAAK,EAAE+B,WAAW,CAAC9B,QAAQ,EAAE8B,WAAW,CAAC7B,QAAQ,CAAC;MAC3G,KAAK,OAAO;QACV,OAAO,MAAMJ,WAAW,CAACQ,iBAAiB,CAACyB,WAAW,CAACxB,KAAK,EAAEwB,WAAW,CAAC9B,QAAQ,EAAE8B,WAAW,CAAC7B,QAAQ,CAAC;MAC3G;QACE,MAAM,IAAI8B,KAAK,CAAC,UAAU,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}