{"ast": null, "code": "var _jsxFileName = \"D:\\\\work\\\\python_work\\\\Family_Takeout\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { message } from 'antd';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // 检查本地存储的token和用户信息\n    const token = localStorage.getItem('access_token');\n    const userData = localStorage.getItem('user');\n    if (token && userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        setUser(parsedUser);\n\n        // 验证token是否有效\n        authService.getProfile().then(userInfo => {\n          setUser(userInfo);\n          localStorage.setItem('user', JSON.stringify(userInfo));\n        }).catch(() => {\n          // token无效，清除本地存储\n          localStorage.removeItem('access_token');\n          localStorage.removeItem('refresh_token');\n          localStorage.removeItem('user');\n          setUser(null);\n        }).finally(() => {\n          setLoading(false);\n        });\n      } catch (error) {\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        localStorage.removeItem('user');\n        setUser(null);\n        setLoading(false);\n      }\n    } else {\n      setLoading(false);\n    }\n  }, []);\n  const login = async (type, credentials) => {\n    try {\n      setLoading(true);\n      const response = await authService.login(type, credentials);\n      if (response.success) {\n        var _response$data, _response$data2, _response$data3;\n        // 处理不同的响应结构\n        const token = response.access_token || ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.token);\n        const refreshToken = response.refresh_token || ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.refresh_token);\n        const userData = response.user || ((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.user);\n        if (token) {\n          localStorage.setItem('access_token', token);\n        }\n        if (refreshToken) {\n          localStorage.setItem('refresh_token', refreshToken);\n        }\n        if (userData) {\n          localStorage.setItem('user', JSON.stringify(userData));\n          setUser(userData);\n        }\n        message.success(response.message);\n        return true;\n      } else {\n        message.error(response.message);\n        return false;\n      }\n    } catch (error) {\n      message.error('登录失败，请稍后重试');\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const register = async (type, credentials) => {\n    try {\n      setLoading(true);\n      const response = await authService.register(type, credentials);\n      if (response.success) {\n        message.success(response.message);\n        return true;\n      } else {\n        message.error(response.message);\n        return false;\n      }\n    } catch (error) {\n      message.error('注册失败，请稍后重试');\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n    localStorage.removeItem('user');\n    setUser(null);\n    message.success('已退出登录');\n  };\n  const updateUser = userData => {\n    if (user) {\n      const updatedUser = {\n        ...user,\n        ...userData\n      };\n      setUser(updatedUser);\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n    }\n  };\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "message", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "getProfile", "then", "userInfo", "setItem", "stringify", "catch", "removeItem", "finally", "error", "login", "type", "credentials", "response", "success", "_response$data", "_response$data2", "_response$data3", "access_token", "data", "refreshToken", "refresh_token", "register", "logout", "updateUser", "updatedUser", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/work/python_work/Family_Takeout/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { message } from 'antd';\r\nimport { authService } from '../services/authService';\r\n\r\ninterface User {\r\n  id: number;\r\n  username: string;\r\n  email?: string;\r\n  phone?: string;\r\n  nickname?: string;\r\n  avatar?: string;\r\n  gender?: string;\r\n  birth_date?: string;\r\n  is_active: boolean;\r\n  is_verified: boolean;\r\n  email_verified: boolean;\r\n  phone_verified: boolean;\r\n  created_at: string;\r\n  last_login?: string;\r\n}\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  loading: boolean;\r\n  login: (type: string, credentials: any) => Promise<boolean>;\r\n  register: (type: string, credentials: any) => Promise<boolean>;\r\n  logout: () => void;\r\n  updateUser: (userData: Partial<User>) => void;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // 检查本地存储的token和用户信息\r\n    const token = localStorage.getItem('access_token');\r\n    const userData = localStorage.getItem('user');\r\n    \r\n    if (token && userData) {\r\n      try {\r\n        const parsedUser = JSON.parse(userData);\r\n        setUser(parsedUser);\r\n        \r\n        // 验证token是否有效\r\n        authService.getProfile()\r\n          .then(userInfo => {\r\n            setUser(userInfo);\r\n            localStorage.setItem('user', JSON.stringify(userInfo));\r\n          })\r\n          .catch(() => {\r\n            // token无效，清除本地存储\r\n            localStorage.removeItem('access_token');\r\n            localStorage.removeItem('refresh_token');\r\n            localStorage.removeItem('user');\r\n            setUser(null);\r\n          })\r\n          .finally(() => {\r\n            setLoading(false);\r\n          });\r\n      } catch (error) {\r\n        localStorage.removeItem('access_token');\r\n        localStorage.removeItem('refresh_token');\r\n        localStorage.removeItem('user');\r\n        setUser(null);\r\n        setLoading(false);\r\n      }\r\n    } else {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const login = async (type: string, credentials: any): Promise<boolean> => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await authService.login(type, credentials);\r\n\r\n      if (response.success) {\r\n        // 处理不同的响应结构\r\n        const token = response.access_token || response.data?.token;\r\n        const refreshToken = response.refresh_token || response.data?.refresh_token;\r\n        const userData = response.user || response.data?.user;\r\n\r\n        if (token) {\r\n          localStorage.setItem('access_token', token);\r\n        }\r\n        if (refreshToken) {\r\n          localStorage.setItem('refresh_token', refreshToken);\r\n        }\r\n        if (userData) {\r\n          localStorage.setItem('user', JSON.stringify(userData));\r\n          setUser(userData);\r\n        }\r\n\r\n        message.success(response.message);\r\n        return true;\r\n      } else {\r\n        message.error(response.message);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      message.error('登录失败，请稍后重试');\r\n      return false;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const register = async (type: string, credentials: any): Promise<boolean> => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await authService.register(type, credentials);\r\n      \r\n      if (response.success) {\r\n        message.success(response.message);\r\n        return true;\r\n      } else {\r\n        message.error(response.message);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      message.error('注册失败，请稍后重试');\r\n      return false;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    localStorage.removeItem('access_token');\r\n    localStorage.removeItem('refresh_token');\r\n    localStorage.removeItem('user');\r\n    setUser(null);\r\n    message.success('已退出登录');\r\n  };\r\n\r\n  const updateUser = (userData: Partial<User>) => {\r\n    if (user) {\r\n      const updatedUser = { ...user, ...userData };\r\n      setUser(updatedUser);\r\n      localStorage.setItem('user', JSON.stringify(updatedUser));\r\n    }\r\n  };\r\n\r\n  const value: AuthContextType = {\r\n    user,\r\n    loading,\r\n    login,\r\n    register,\r\n    logout,\r\n    updateUser,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n}; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4BtD,MAAMC,WAAW,gBAAGR,aAAa,CAA8BS,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGX,UAAU,CAACO,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMkB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE7C,IAAIF,KAAK,IAAIG,QAAQ,EAAE;MACrB,IAAI;QACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;QACvCN,OAAO,CAACO,UAAU,CAAC;;QAEnB;QACApB,WAAW,CAACuB,UAAU,CAAC,CAAC,CACrBC,IAAI,CAACC,QAAQ,IAAI;UAChBZ,OAAO,CAACY,QAAQ,CAAC;UACjBR,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEL,IAAI,CAACM,SAAS,CAACF,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CACDG,KAAK,CAAC,MAAM;UACX;UACAX,YAAY,CAACY,UAAU,CAAC,cAAc,CAAC;UACvCZ,YAAY,CAACY,UAAU,CAAC,eAAe,CAAC;UACxCZ,YAAY,CAACY,UAAU,CAAC,MAAM,CAAC;UAC/BhB,OAAO,CAAC,IAAI,CAAC;QACf,CAAC,CAAC,CACDiB,OAAO,CAAC,MAAM;UACbf,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,CAAC;MACN,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACdd,YAAY,CAACY,UAAU,CAAC,cAAc,CAAC;QACvCZ,YAAY,CAACY,UAAU,CAAC,eAAe,CAAC;QACxCZ,YAAY,CAACY,UAAU,CAAC,MAAM,CAAC;QAC/BhB,OAAO,CAAC,IAAI,CAAC;QACbE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,MAAM;MACLA,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,KAAK,GAAG,MAAAA,CAAOC,IAAY,EAAEC,WAAgB,KAAuB;IACxE,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoB,QAAQ,GAAG,MAAMnC,WAAW,CAACgC,KAAK,CAACC,IAAI,EAAEC,WAAW,CAAC;MAE3D,IAAIC,QAAQ,CAACC,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA;QACpB;QACA,MAAMvB,KAAK,GAAGmB,QAAQ,CAACK,YAAY,MAAAH,cAAA,GAAIF,QAAQ,CAACM,IAAI,cAAAJ,cAAA,uBAAbA,cAAA,CAAerB,KAAK;QAC3D,MAAM0B,YAAY,GAAGP,QAAQ,CAACQ,aAAa,MAAAL,eAAA,GAAIH,QAAQ,CAACM,IAAI,cAAAH,eAAA,uBAAbA,eAAA,CAAeK,aAAa;QAC3E,MAAMxB,QAAQ,GAAGgB,QAAQ,CAACvB,IAAI,MAAA2B,eAAA,GAAIJ,QAAQ,CAACM,IAAI,cAAAF,eAAA,uBAAbA,eAAA,CAAe3B,IAAI;QAErD,IAAII,KAAK,EAAE;UACTC,YAAY,CAACS,OAAO,CAAC,cAAc,EAAEV,KAAK,CAAC;QAC7C;QACA,IAAI0B,YAAY,EAAE;UAChBzB,YAAY,CAACS,OAAO,CAAC,eAAe,EAAEgB,YAAY,CAAC;QACrD;QACA,IAAIvB,QAAQ,EAAE;UACZF,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEL,IAAI,CAACM,SAAS,CAACR,QAAQ,CAAC,CAAC;UACtDN,OAAO,CAACM,QAAQ,CAAC;QACnB;QAEApB,OAAO,CAACqC,OAAO,CAACD,QAAQ,CAACpC,OAAO,CAAC;QACjC,OAAO,IAAI;MACb,CAAC,MAAM;QACLA,OAAO,CAACgC,KAAK,CAACI,QAAQ,CAACpC,OAAO,CAAC;QAC/B,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,YAAY,CAAC;MAC3B,OAAO,KAAK;IACd,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,QAAQ,GAAG,MAAAA,CAAOX,IAAY,EAAEC,WAAgB,KAAuB;IAC3E,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoB,QAAQ,GAAG,MAAMnC,WAAW,CAAC4C,QAAQ,CAACX,IAAI,EAAEC,WAAW,CAAC;MAE9D,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpBrC,OAAO,CAACqC,OAAO,CAACD,QAAQ,CAACpC,OAAO,CAAC;QACjC,OAAO,IAAI;MACb,CAAC,MAAM;QACLA,OAAO,CAACgC,KAAK,CAACI,QAAQ,CAACpC,OAAO,CAAC;QAC/B,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,YAAY,CAAC;MAC3B,OAAO,KAAK;IACd,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,MAAM,GAAGA,CAAA,KAAM;IACnB5B,YAAY,CAACY,UAAU,CAAC,cAAc,CAAC;IACvCZ,YAAY,CAACY,UAAU,CAAC,eAAe,CAAC;IACxCZ,YAAY,CAACY,UAAU,CAAC,MAAM,CAAC;IAC/BhB,OAAO,CAAC,IAAI,CAAC;IACbd,OAAO,CAACqC,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;EAED,MAAMU,UAAU,GAAI3B,QAAuB,IAAK;IAC9C,IAAIP,IAAI,EAAE;MACR,MAAMmC,WAAW,GAAG;QAAE,GAAGnC,IAAI;QAAE,GAAGO;MAAS,CAAC;MAC5CN,OAAO,CAACkC,WAAW,CAAC;MACpB9B,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEL,IAAI,CAACM,SAAS,CAACoB,WAAW,CAAC,CAAC;IAC3D;EACF,CAAC;EAED,MAAMC,KAAsB,GAAG;IAC7BpC,IAAI;IACJE,OAAO;IACPkB,KAAK;IACLY,QAAQ;IACRC,MAAM;IACNC;EACF,CAAC;EAED,oBACE5C,OAAA,CAACC,WAAW,CAAC8C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAtC,QAAA,EAChCA;EAAQ;IAAAwC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC1C,GAAA,CAhIWF,YAAyC;AAAA6C,EAAA,GAAzC7C,YAAyC;AAAA,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}