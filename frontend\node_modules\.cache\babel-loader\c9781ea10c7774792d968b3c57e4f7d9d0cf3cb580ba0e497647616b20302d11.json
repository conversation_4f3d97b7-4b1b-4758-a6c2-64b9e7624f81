{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000\n});\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('access_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理token过期\napi.interceptors.response.use(response => {\n  return response;\n}, async error => {\n  var _error$response;\n  const originalRequest = error.config;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n    originalRequest._retry = true;\n    const refreshToken = localStorage.getItem('refresh_token');\n    if (refreshToken) {\n      try {\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\n          refresh_token: refreshToken\n        });\n        const {\n          access_token\n        } = response.data;\n        localStorage.setItem('access_token', access_token);\n        originalRequest.headers.Authorization = `Bearer ${access_token}`;\n        return api(originalRequest);\n      } catch (refreshError) {\n        // 刷新token失败，清除本地存储\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n  }\n  return Promise.reject(error);\n});\nexport const authService = {\n  // 邮箱注册\n  registerWithEmail: async (email, password, username) => {\n    // 模拟API调用延迟\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // 检查邮箱是否已存在（模拟）\n    const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');\n    const emailExists = existingUsers.some(user => user.email === email);\n    if (emailExists) {\n      return {\n        success: false,\n        message: '该邮箱已被注册，请使用其他邮箱'\n      };\n    }\n\n    // 创建新用户\n    const newUser = {\n      id: Date.now(),\n      username: username || email.split('@')[0],\n      email,\n      password,\n      // 实际项目中应该加密\n      avatar: null,\n      phone: null,\n      createdAt: new Date().toISOString()\n    };\n\n    // 保存到本地存储\n    existingUsers.push(newUser);\n    localStorage.setItem('users', JSON.stringify(existingUsers));\n    return {\n      success: true,\n      message: '注册成功！请登录'\n    };\n  },\n  // 手机号注册\n  registerWithPhone: async (phone, password, username) => {\n    // 模拟API调用延迟\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // 检查手机号是否已存在（模拟）\n    const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');\n    const phoneExists = existingUsers.some(user => user.phone === phone);\n    if (phoneExists) {\n      return {\n        success: false,\n        message: '该手机号已被注册，请使用其他手机号'\n      };\n    }\n\n    // 创建新用户\n    const newUser = {\n      id: Date.now(),\n      username: username || `用户${phone.slice(-4)}`,\n      phone,\n      password,\n      // 实际项目中应该加密\n      avatar: null,\n      email: null,\n      createdAt: new Date().toISOString()\n    };\n\n    // 保存到本地存储\n    existingUsers.push(newUser);\n    localStorage.setItem('users', JSON.stringify(existingUsers));\n    return {\n      success: true,\n      message: '注册成功！请登录'\n    };\n  },\n  // 邮箱登录\n  loginWithEmail: async (email, password) => {\n    // 模拟API调用延迟\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    // 从本地存储获取用户数据\n    const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');\n    const user = existingUsers.find(u => u.email === email && u.password === password);\n    if (user) {\n      return {\n        success: true,\n        message: '登录成功',\n        data: {\n          user: {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            phone: user.phone,\n            avatar: user.avatar\n          },\n          token: `mock_token_${user.id}_${Date.now()}`\n        }\n      };\n    } else {\n      return {\n        success: false,\n        message: '邮箱或密码错误'\n      };\n    }\n  },\n  // 手机号登录\n  loginWithPhone: async (phone, password) => {\n    // 模拟API调用延迟\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    // 从本地存储获取用户数据\n    const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');\n    const user = existingUsers.find(u => u.phone === phone && u.password === password);\n    if (user) {\n      return {\n        success: true,\n        message: '登录成功',\n        data: {\n          user: {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            phone: user.phone,\n            avatar: user.avatar\n          },\n          token: `mock_token_${user.id}_${Date.now()}`\n        }\n      };\n    } else {\n      return {\n        success: false,\n        message: '手机号或密码错误'\n      };\n    }\n  },\n  // 验证码登录\n  loginWithVerificationCode: async (phone, code) => {\n    const response = await api.post('/auth/login/verification-code', {\n      phone,\n      code\n    });\n    return response.data;\n  },\n  // 微信登录\n  loginWithWechat: async code => {\n    const response = await api.post('/auth/login/wechat', {\n      code\n    });\n    return response.data;\n  },\n  // 支付宝登录\n  loginWithAlipay: async authCode => {\n    const response = await api.post('/auth/login/alipay', {\n      auth_code: authCode\n    });\n    return response.data;\n  },\n  // 发送邮箱验证码\n  sendEmailVerification: async email => {\n    const response = await api.post('/auth/send-email-code', {\n      email\n    });\n    return response.data;\n  },\n  // 发送短信验证码\n  sendSmsVerification: async phone => {\n    const response = await api.post('/auth/send-sms-code', {\n      phone\n    });\n    return response.data;\n  },\n  // 获取用户信息\n  getProfile: async () => {\n    const response = await api.get('/auth/profile');\n    return response.data.user;\n  },\n  // 登出\n  logout: async () => {\n    const response = await api.post('/auth/logout');\n    return response.data;\n  },\n  // 通用登录方法\n  login: async (type, credentials) => {\n    switch (type) {\n      case 'email':\n        return await authService.loginWithEmail(credentials.email, credentials.password);\n      case 'phone':\n        return await authService.loginWithPhone(credentials.phone, credentials.password);\n      case 'verification_code':\n        return await authService.loginWithVerificationCode(credentials.phone, credentials.code);\n      case 'wechat':\n        return await authService.loginWithWechat(credentials.code);\n      case 'alipay':\n        return await authService.loginWithAlipay(credentials.authCode);\n      default:\n        throw new Error('不支持的登录类型');\n    }\n  },\n  // 通用注册方法\n  register: async (type, credentials) => {\n    switch (type) {\n      case 'email':\n        return await authService.registerWithEmail(credentials.email, credentials.password, credentials.username);\n      case 'phone':\n        return await authService.registerWithPhone(credentials.phone, credentials.password, credentials.username);\n      default:\n        throw new Error('不支持的注册类型');\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "post", "refresh_token", "access_token", "data", "setItem", "refreshError", "removeItem", "window", "location", "href", "authService", "registerWithEmail", "email", "password", "username", "resolve", "setTimeout", "existingUsers", "JSON", "parse", "emailExists", "some", "user", "success", "message", "newUser", "id", "Date", "now", "split", "avatar", "phone", "createdAt", "toISOString", "push", "stringify", "registerWithPhone", "phoneExists", "slice", "loginWithEmail", "find", "u", "loginWithPhone", "loginWithVerificationCode", "code", "loginWithWechat", "loginWithAlipay", "authCode", "auth_code", "sendEmailVerification", "sendSmsVerification", "getProfile", "get", "logout", "login", "type", "credentials", "Error", "register"], "sources": ["D:/work/python_work/Family_Takeout/frontend/src/services/authService.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\r\n\r\n// 创建axios实例\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 10000,\r\n});\r\n\r\n// 请求拦截器 - 添加token\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('access_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 响应拦截器 - 处理token过期\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      const refreshToken = localStorage.getItem('refresh_token');\r\n      if (refreshToken) {\r\n        try {\r\n          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {\r\n            refresh_token: refreshToken,\r\n          });\r\n          \r\n          const { access_token } = response.data;\r\n          localStorage.setItem('access_token', access_token);\r\n          \r\n          originalRequest.headers.Authorization = `Bearer ${access_token}`;\r\n          return api(originalRequest);\r\n        } catch (refreshError) {\r\n          // 刷新token失败，清除本地存储\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          localStorage.removeItem('user');\r\n          window.location.href = '/login';\r\n          return Promise.reject(refreshError);\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport const authService = {\r\n  // 邮箱注册\r\n  registerWithEmail: async (email: string, password: string, username?: string) => {\r\n    // 模拟API调用延迟\r\n    await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n    // 检查邮箱是否已存在（模拟）\r\n    const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');\r\n    const emailExists = existingUsers.some((user: any) => user.email === email);\r\n\r\n    if (emailExists) {\r\n      return {\r\n        success: false,\r\n        message: '该邮箱已被注册，请使用其他邮箱'\r\n      };\r\n    }\r\n\r\n    // 创建新用户\r\n    const newUser = {\r\n      id: Date.now(),\r\n      username: username || email.split('@')[0],\r\n      email,\r\n      password, // 实际项目中应该加密\r\n      avatar: null,\r\n      phone: null,\r\n      createdAt: new Date().toISOString()\r\n    };\r\n\r\n    // 保存到本地存储\r\n    existingUsers.push(newUser);\r\n    localStorage.setItem('users', JSON.stringify(existingUsers));\r\n\r\n    return {\r\n      success: true,\r\n      message: '注册成功！请登录'\r\n    };\r\n  },\r\n\r\n  // 手机号注册\r\n  registerWithPhone: async (phone: string, password: string, username?: string) => {\r\n    // 模拟API调用延迟\r\n    await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n    // 检查手机号是否已存在（模拟）\r\n    const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');\r\n    const phoneExists = existingUsers.some((user: any) => user.phone === phone);\r\n\r\n    if (phoneExists) {\r\n      return {\r\n        success: false,\r\n        message: '该手机号已被注册，请使用其他手机号'\r\n      };\r\n    }\r\n\r\n    // 创建新用户\r\n    const newUser = {\r\n      id: Date.now(),\r\n      username: username || `用户${phone.slice(-4)}`,\r\n      phone,\r\n      password, // 实际项目中应该加密\r\n      avatar: null,\r\n      email: null,\r\n      createdAt: new Date().toISOString()\r\n    };\r\n\r\n    // 保存到本地存储\r\n    existingUsers.push(newUser);\r\n    localStorage.setItem('users', JSON.stringify(existingUsers));\r\n\r\n    return {\r\n      success: true,\r\n      message: '注册成功！请登录'\r\n    };\r\n  },\r\n\r\n  // 邮箱登录\r\n  loginWithEmail: async (email: string, password: string) => {\r\n    // 模拟API调用延迟\r\n    await new Promise(resolve => setTimeout(resolve, 800));\r\n\r\n    // 从本地存储获取用户数据\r\n    const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');\r\n    const user = existingUsers.find((u: any) => u.email === email && u.password === password);\r\n\r\n    if (user) {\r\n      return {\r\n        success: true,\r\n        message: '登录成功',\r\n        data: {\r\n          user: {\r\n            id: user.id,\r\n            username: user.username,\r\n            email: user.email,\r\n            phone: user.phone,\r\n            avatar: user.avatar\r\n          },\r\n          token: `mock_token_${user.id}_${Date.now()}`\r\n        }\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        message: '邮箱或密码错误'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 手机号登录\r\n  loginWithPhone: async (phone: string, password: string) => {\r\n    // 模拟API调用延迟\r\n    await new Promise(resolve => setTimeout(resolve, 800));\r\n\r\n    // 从本地存储获取用户数据\r\n    const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');\r\n    const user = existingUsers.find((u: any) => u.phone === phone && u.password === password);\r\n\r\n    if (user) {\r\n      return {\r\n        success: true,\r\n        message: '登录成功',\r\n        data: {\r\n          user: {\r\n            id: user.id,\r\n            username: user.username,\r\n            email: user.email,\r\n            phone: user.phone,\r\n            avatar: user.avatar\r\n          },\r\n          token: `mock_token_${user.id}_${Date.now()}`\r\n        }\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        message: '手机号或密码错误'\r\n      };\r\n    }\r\n  },\r\n\r\n  // 验证码登录\r\n  loginWithVerificationCode: async (phone: string, code: string) => {\r\n    const response = await api.post('/auth/login/verification-code', {\r\n      phone,\r\n      code,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 微信登录\r\n  loginWithWechat: async (code: string) => {\r\n    const response = await api.post('/auth/login/wechat', {\r\n      code,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 支付宝登录\r\n  loginWithAlipay: async (authCode: string) => {\r\n    const response = await api.post('/auth/login/alipay', {\r\n      auth_code: authCode,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 发送邮箱验证码\r\n  sendEmailVerification: async (email: string) => {\r\n    const response = await api.post('/auth/send-email-code', {\r\n      email,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 发送短信验证码\r\n  sendSmsVerification: async (phone: string) => {\r\n    const response = await api.post('/auth/send-sms-code', {\r\n      phone,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  // 获取用户信息\r\n  getProfile: async () => {\r\n    const response = await api.get('/auth/profile');\r\n    return response.data.user;\r\n  },\r\n\r\n  // 登出\r\n  logout: async () => {\r\n    const response = await api.post('/auth/logout');\r\n    return response.data;\r\n  },\r\n\r\n  // 通用登录方法\r\n  login: async (type: string, credentials: any) => {\r\n    switch (type) {\r\n      case 'email':\r\n        return await authService.loginWithEmail(credentials.email, credentials.password);\r\n      case 'phone':\r\n        return await authService.loginWithPhone(credentials.phone, credentials.password);\r\n      case 'verification_code':\r\n        return await authService.loginWithVerificationCode(credentials.phone, credentials.code);\r\n      case 'wechat':\r\n        return await authService.loginWithWechat(credentials.code);\r\n      case 'alipay':\r\n        return await authService.loginWithAlipay(credentials.authCode);\r\n      default:\r\n        throw new Error('不支持的登录类型');\r\n    }\r\n  },\r\n\r\n  // 通用注册方法\r\n  register: async (type: string, credentials: any) => {\r\n    switch (type) {\r\n      case 'email':\r\n        return await authService.registerWithEmail(credentials.email, credentials.password, credentials.username);\r\n      case 'phone':\r\n        return await authService.registerWithPhone(credentials.phone, credentials.password, credentials.username);\r\n      default:\r\n        throw new Error('不支持的注册类型');\r\n    }\r\n  },\r\n}; "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACI,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACD,MAAOH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACf,MAAMC,eAAe,GAAGL,KAAK,CAACN,MAAM;EAEpC,IAAI,EAAAU,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;IAE7B,MAAMC,YAAY,GAAGZ,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC1D,IAAIW,YAAY,EAAE;MAChB,IAAI;QACF,MAAML,QAAQ,GAAG,MAAMrB,KAAK,CAAC2B,IAAI,CAAC,GAAG1B,YAAY,eAAe,EAAE;UAChE2B,aAAa,EAAEF;QACjB,CAAC,CAAC;QAEF,MAAM;UAAEG;QAAa,CAAC,GAAGR,QAAQ,CAACS,IAAI;QACtChB,YAAY,CAACiB,OAAO,CAAC,cAAc,EAAEF,YAAY,CAAC;QAElDN,eAAe,CAACP,OAAO,CAACC,aAAa,GAAG,UAAUY,YAAY,EAAE;QAChE,OAAOxB,GAAG,CAACkB,eAAe,CAAC;MAC7B,CAAC,CAAC,OAAOS,YAAY,EAAE;QACrB;QACAlB,YAAY,CAACmB,UAAU,CAAC,cAAc,CAAC;QACvCnB,YAAY,CAACmB,UAAU,CAAC,eAAe,CAAC;QACxCnB,YAAY,CAACmB,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B,OAAOjB,OAAO,CAACC,MAAM,CAACY,YAAY,CAAC;MACrC;IACF;EACF;EAEA,OAAOb,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,OAAO,MAAMmB,WAAW,GAAG;EACzB;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,EAAEC,QAAiB,KAAK;IAC/E;IACA,MAAM,IAAItB,OAAO,CAACuB,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAChC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IACvE,MAAMgC,WAAW,GAAGH,aAAa,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACV,KAAK,KAAKA,KAAK,CAAC;IAE3E,IAAIQ,WAAW,EAAE;MACf,OAAO;QACLG,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;;IAEA;IACA,MAAMC,OAAO,GAAG;MACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdd,QAAQ,EAAEA,QAAQ,IAAIF,KAAK,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzCjB,KAAK;MACLC,QAAQ;MAAE;MACViB,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;IACpC,CAAC;;IAED;IACAhB,aAAa,CAACiB,IAAI,CAACT,OAAO,CAAC;IAC3BtC,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEc,IAAI,CAACiB,SAAS,CAAClB,aAAa,CAAC,CAAC;IAE5D,OAAO;MACLM,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED;EACAY,iBAAiB,EAAE,MAAAA,CAAOL,KAAa,EAAElB,QAAgB,EAAEC,QAAiB,KAAK;IAC/E;IACA,MAAM,IAAItB,OAAO,CAACuB,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAChC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IACvE,MAAMiD,WAAW,GAAGpB,aAAa,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACS,KAAK,KAAKA,KAAK,CAAC;IAE3E,IAAIM,WAAW,EAAE;MACf,OAAO;QACLd,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;;IAEA;IACA,MAAMC,OAAO,GAAG;MACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdd,QAAQ,EAAEA,QAAQ,IAAI,KAAKiB,KAAK,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5CP,KAAK;MACLlB,QAAQ;MAAE;MACViB,MAAM,EAAE,IAAI;MACZlB,KAAK,EAAE,IAAI;MACXoB,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;IACpC,CAAC;;IAED;IACAhB,aAAa,CAACiB,IAAI,CAACT,OAAO,CAAC;IAC3BtC,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEc,IAAI,CAACiB,SAAS,CAAClB,aAAa,CAAC,CAAC;IAE5D,OAAO;MACLM,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED;EACAe,cAAc,EAAE,MAAAA,CAAO3B,KAAa,EAAEC,QAAgB,KAAK;IACzD;IACA,MAAM,IAAIrB,OAAO,CAACuB,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;IAEtD;IACA,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAChC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IACvE,MAAMkC,IAAI,GAAGL,aAAa,CAACuB,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAAC7B,KAAK,KAAKA,KAAK,IAAI6B,CAAC,CAAC5B,QAAQ,KAAKA,QAAQ,CAAC;IAEzF,IAAIS,IAAI,EAAE;MACR,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,MAAM;QACfrB,IAAI,EAAE;UACJmB,IAAI,EAAE;YACJI,EAAE,EAAEJ,IAAI,CAACI,EAAE;YACXZ,QAAQ,EAAEQ,IAAI,CAACR,QAAQ;YACvBF,KAAK,EAAEU,IAAI,CAACV,KAAK;YACjBmB,KAAK,EAAET,IAAI,CAACS,KAAK;YACjBD,MAAM,EAAER,IAAI,CAACQ;UACf,CAAC;UACD5C,KAAK,EAAE,cAAcoC,IAAI,CAACI,EAAE,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC5C;MACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLL,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAkB,cAAc,EAAE,MAAAA,CAAOX,KAAa,EAAElB,QAAgB,KAAK;IACzD;IACA,MAAM,IAAIrB,OAAO,CAACuB,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;IAEtD;IACA,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAChC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IACvE,MAAMkC,IAAI,GAAGL,aAAa,CAACuB,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACV,KAAK,KAAKA,KAAK,IAAIU,CAAC,CAAC5B,QAAQ,KAAKA,QAAQ,CAAC;IAEzF,IAAIS,IAAI,EAAE;MACR,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,MAAM;QACfrB,IAAI,EAAE;UACJmB,IAAI,EAAE;YACJI,EAAE,EAAEJ,IAAI,CAACI,EAAE;YACXZ,QAAQ,EAAEQ,IAAI,CAACR,QAAQ;YACvBF,KAAK,EAAEU,IAAI,CAACV,KAAK;YACjBmB,KAAK,EAAET,IAAI,CAACS,KAAK;YACjBD,MAAM,EAAER,IAAI,CAACQ;UACf,CAAC;UACD5C,KAAK,EAAE,cAAcoC,IAAI,CAACI,EAAE,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC5C;MACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLL,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAmB,yBAAyB,EAAE,MAAAA,CAAOZ,KAAa,EAAEa,IAAY,KAAK;IAChE,MAAMlD,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,+BAA+B,EAAE;MAC/D+B,KAAK;MACLa;IACF,CAAC,CAAC;IACF,OAAOlD,QAAQ,CAACS,IAAI;EACtB,CAAC;EAED;EACA0C,eAAe,EAAE,MAAOD,IAAY,IAAK;IACvC,MAAMlD,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,oBAAoB,EAAE;MACpD4C;IACF,CAAC,CAAC;IACF,OAAOlD,QAAQ,CAACS,IAAI;EACtB,CAAC;EAED;EACA2C,eAAe,EAAE,MAAOC,QAAgB,IAAK;IAC3C,MAAMrD,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,oBAAoB,EAAE;MACpDgD,SAAS,EAAED;IACb,CAAC,CAAC;IACF,OAAOrD,QAAQ,CAACS,IAAI;EACtB,CAAC;EAED;EACA8C,qBAAqB,EAAE,MAAOrC,KAAa,IAAK;IAC9C,MAAMlB,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,uBAAuB,EAAE;MACvDY;IACF,CAAC,CAAC;IACF,OAAOlB,QAAQ,CAACS,IAAI;EACtB,CAAC;EAED;EACA+C,mBAAmB,EAAE,MAAOnB,KAAa,IAAK;IAC5C,MAAMrC,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,qBAAqB,EAAE;MACrD+B;IACF,CAAC,CAAC;IACF,OAAOrC,QAAQ,CAACS,IAAI;EACtB,CAAC;EAED;EACAgD,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,MAAMzD,QAAQ,GAAG,MAAMhB,GAAG,CAAC0E,GAAG,CAAC,eAAe,CAAC;IAC/C,OAAO1D,QAAQ,CAACS,IAAI,CAACmB,IAAI;EAC3B,CAAC;EAED;EACA+B,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,MAAM3D,QAAQ,GAAG,MAAMhB,GAAG,CAACsB,IAAI,CAAC,cAAc,CAAC;IAC/C,OAAON,QAAQ,CAACS,IAAI;EACtB,CAAC;EAED;EACAmD,KAAK,EAAE,MAAAA,CAAOC,IAAY,EAAEC,WAAgB,KAAK;IAC/C,QAAQD,IAAI;MACV,KAAK,OAAO;QACV,OAAO,MAAM7C,WAAW,CAAC6B,cAAc,CAACiB,WAAW,CAAC5C,KAAK,EAAE4C,WAAW,CAAC3C,QAAQ,CAAC;MAClF,KAAK,OAAO;QACV,OAAO,MAAMH,WAAW,CAACgC,cAAc,CAACc,WAAW,CAACzB,KAAK,EAAEyB,WAAW,CAAC3C,QAAQ,CAAC;MAClF,KAAK,mBAAmB;QACtB,OAAO,MAAMH,WAAW,CAACiC,yBAAyB,CAACa,WAAW,CAACzB,KAAK,EAAEyB,WAAW,CAACZ,IAAI,CAAC;MACzF,KAAK,QAAQ;QACX,OAAO,MAAMlC,WAAW,CAACmC,eAAe,CAACW,WAAW,CAACZ,IAAI,CAAC;MAC5D,KAAK,QAAQ;QACX,OAAO,MAAMlC,WAAW,CAACoC,eAAe,CAACU,WAAW,CAACT,QAAQ,CAAC;MAChE;QACE,MAAM,IAAIU,KAAK,CAAC,UAAU,CAAC;IAC/B;EACF,CAAC;EAED;EACAC,QAAQ,EAAE,MAAAA,CAAOH,IAAY,EAAEC,WAAgB,KAAK;IAClD,QAAQD,IAAI;MACV,KAAK,OAAO;QACV,OAAO,MAAM7C,WAAW,CAACC,iBAAiB,CAAC6C,WAAW,CAAC5C,KAAK,EAAE4C,WAAW,CAAC3C,QAAQ,EAAE2C,WAAW,CAAC1C,QAAQ,CAAC;MAC3G,KAAK,OAAO;QACV,OAAO,MAAMJ,WAAW,CAAC0B,iBAAiB,CAACoB,WAAW,CAACzB,KAAK,EAAEyB,WAAW,CAAC3C,QAAQ,EAAE2C,WAAW,CAAC1C,QAAQ,CAAC;MAC3G;QACE,MAAM,IAAI2C,KAAK,CAAC,UAAU,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}