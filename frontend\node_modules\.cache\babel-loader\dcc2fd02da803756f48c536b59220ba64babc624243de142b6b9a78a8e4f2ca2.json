{"ast": null, "code": "var _jsxFileName = \"D:\\\\work\\\\python_work\\\\Family_Takeout\\\\frontend\\\\src\\\\pages\\\\RegisterPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Tabs, message, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, WechatOutlined, AlipayOutlined } from '@ant-design/icons';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst RegisterPage = () => {\n  _s();\n  const [emailForm] = Form.useForm();\n  const [phoneForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const {\n    register\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleEmailRegister = async values => {\n    try {\n      setLoading(true);\n      const success = await register('email', values);\n      if (success) {\n        navigate('/login');\n      }\n    } catch (error) {\n      message.error('注册失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePhoneRegister = async values => {\n    try {\n      setLoading(true);\n      const success = await register('phone', values);\n      if (success) {\n        navigate('/login');\n      }\n    } catch (error) {\n      message.error('注册失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSendEmailCode = async () => {\n    try {\n      const email = emailForm.getFieldValue('email');\n      if (!email) {\n        message.error('请输入邮箱');\n        return;\n      }\n      const response = await authService.sendEmailVerification(email);\n      if (response.success) {\n        message.success('验证码已发送到邮箱');\n        setCountdown(60);\n        const timer = setInterval(() => {\n          setCountdown(prev => {\n            if (prev <= 1) {\n              clearInterval(timer);\n              return 0;\n            }\n            return prev - 1;\n          });\n        }, 1000);\n      } else {\n        message.error(response.message || '发送验证码失败');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('发送邮箱验证码错误:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || '发送验证码失败';\n      message.error(errorMessage);\n    }\n  };\n  const handleSendSmsCode = async () => {\n    try {\n      const phone = phoneForm.getFieldValue('phone');\n      if (!phone) {\n        message.error('请输入手机号');\n        return;\n      }\n      const response = await authService.sendSmsVerification(phone);\n      if (response.success) {\n        message.success('验证码已发送');\n        setCountdown(60);\n        const timer = setInterval(() => {\n          setCountdown(prev => {\n            if (prev <= 1) {\n              clearInterval(timer);\n              return 0;\n            }\n            return prev - 1;\n          });\n        }, 1000);\n      } else {\n        message.error(response.message || '发送验证码失败');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('发送短信验证码错误:', error);\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || '发送验证码失败';\n      message.error(errorMessage);\n    }\n  };\n  const handleWechatRegister = () => {\n    // 这里需要集成微信登录SDK\n    message.info('微信注册功能开发中...');\n  };\n  const handleAlipayRegister = () => {\n    // 这里需要集成支付宝登录SDK\n    message.info('支付宝注册功能开发中...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\u521B\\u5EFA\\u8D26\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u52A0\\u5165\\u5BB6\\u5EAD\\u5916\\u5356\\u7CFB\\u7EDF\\uFF0C\\u4EAB\\u53D7\\u4FBF\\u6377\\u670D\\u52A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        defaultActiveKey: \"email\",\n        centered: true,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u90AE\\u7BB1\\u6CE8\\u518C\",\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: emailForm,\n            name: \"email_register\",\n            onFinish: handleEmailRegister,\n            autoComplete: \"off\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 2,\n                message: '用户名长度不能少于2位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"email\",\n              rules: [{\n                required: true,\n                message: '请输入邮箱'\n              }, {\n                type: 'email',\n                message: '请输入有效的邮箱地址'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                message: '密码长度不能少于6位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"confirmPassword\",\n              dependencies: ['password'],\n              rules: [{\n                required: true,\n                message: '请确认密码'\n              }, ({\n                getFieldValue\n              }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('password') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                }\n              })],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u786E\\u8BA4\\u5BC6\\u7801\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                size: \"large\",\n                loading: loading,\n                className: \"submit-btn\",\n                children: \"\\u6CE8\\u518C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)\n        }, \"email\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u624B\\u673A\\u53F7\\u6CE8\\u518C\",\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: phoneForm,\n            name: \"phone_register\",\n            onFinish: handlePhoneRegister,\n            autoComplete: \"off\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 2,\n                message: '用户名长度不能少于2位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"phone\",\n              rules: [{\n                required: true,\n                message: '请输入手机号'\n              }, {\n                pattern: /^1[3-9]\\d{9}$/,\n                message: '请输入有效的手机号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(PhoneOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u624B\\u673A\\u53F7\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                message: '密码长度不能少于6位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"confirmPassword\",\n              dependencies: ['password'],\n              rules: [{\n                required: true,\n                message: '请确认密码'\n              }, ({\n                getFieldValue\n              }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('password') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                }\n              })],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u786E\\u8BA4\\u5BC6\\u7801\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                size: \"large\",\n                loading: loading,\n                className: \"submit-btn\",\n                children: \"\\u6CE8\\u518C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, \"phone\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u6216\\u4F7F\\u7528\\u7B2C\\u4E09\\u65B9\\u6CE8\\u518C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"social-login-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(WechatOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 19\n          }, this),\n          className: \"wechat-btn\",\n          size: \"large\",\n          onClick: handleWechatRegister,\n          children: \"\\u5FAE\\u4FE1\\u6CE8\\u518C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(AlipayOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 19\n          }, this),\n          className: \"alipay-btn\",\n          size: \"large\",\n          onClick: handleAlipayRegister,\n          children: \"\\u652F\\u4ED8\\u5B9D\\u6CE8\\u518C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"switch-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u5DF2\\u6709\\u8D26\\u6237\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          children: \"\\u7ACB\\u5373\\u767B\\u5F55\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"7iPXGeAieYOooMPS8vOdBYkUcDQ=\", false, function () {\n  return [Form.useForm, Form.useForm, useAuth, useNavigate];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Tabs", "message", "Divider", "UserOutlined", "LockOutlined", "MailOutlined", "PhoneOutlined", "WechatOutlined", "AlipayOutlined", "Link", "useNavigate", "useAuth", "authService", "jsxDEV", "_jsxDEV", "TabPane", "RegisterPage", "_s", "emailForm", "useForm", "phoneForm", "loading", "setLoading", "countdown", "setCountdown", "register", "navigate", "handleEmailRegister", "values", "success", "error", "handlePhoneRegister", "handleSendEmailCode", "email", "getFieldValue", "response", "sendEmailVerification", "timer", "setInterval", "prev", "clearInterval", "_error$response", "_error$response$data", "console", "errorMessage", "data", "handleSendSmsCode", "phone", "sendSmsVerification", "_error$response2", "_error$response2$data", "handleWechatRegister", "info", "handleAlipayRegister", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultActiveKey", "centered", "tab", "form", "name", "onFinish", "autoComplete", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "size", "type", "Password", "dependencies", "validator", "_", "value", "Promise", "resolve", "reject", "Error", "htmlType", "pattern", "icon", "onClick", "to", "_c", "$RefreshReg$"], "sources": ["D:/work/python_work/Family_Takeout/frontend/src/pages/RegisterPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Form, Input, Button, Tabs, message, Divider } from 'antd';\r\nimport { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, WechatOutlined, AlipayOutlined } from '@ant-design/icons';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../contexts/AuthContext';\r\nimport { authService } from '../services/authService';\r\n\r\nconst { TabPane } = Tabs;\r\n\r\nconst RegisterPage: React.FC = () => {\r\n  const [emailForm] = Form.useForm();\r\n  const [phoneForm] = Form.useForm();\r\n  const [loading, setLoading] = useState(false);\r\n  const [countdown, setCountdown] = useState(0);\r\n  const { register } = useAuth();\r\n  const navigate = useNavigate();\r\n\r\n  const handleEmailRegister = async (values: any) => {\r\n    try {\r\n      setLoading(true);\r\n      const success = await register('email', values);\r\n      if (success) {\r\n        navigate('/login');\r\n      }\r\n    } catch (error) {\r\n      message.error('注册失败，请稍后重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePhoneRegister = async (values: any) => {\r\n    try {\r\n      setLoading(true);\r\n      const success = await register('phone', values);\r\n      if (success) {\r\n        navigate('/login');\r\n      }\r\n    } catch (error) {\r\n      message.error('注册失败，请稍后重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSendEmailCode = async () => {\r\n    try {\r\n      const email = emailForm.getFieldValue('email');\r\n      if (!email) {\r\n        message.error('请输入邮箱');\r\n        return;\r\n      }\r\n\r\n      const response = await authService.sendEmailVerification(email);\r\n      if (response.success) {\r\n        message.success('验证码已发送到邮箱');\r\n        setCountdown(60);\r\n        const timer = setInterval(() => {\r\n          setCountdown((prev) => {\r\n            if (prev <= 1) {\r\n              clearInterval(timer);\r\n              return 0;\r\n            }\r\n            return prev - 1;\r\n          });\r\n        }, 1000);\r\n      } else {\r\n        message.error(response.message || '发送验证码失败');\r\n      }\r\n    } catch (error: any) {\r\n      console.error('发送邮箱验证码错误:', error);\r\n      const errorMessage = error.response?.data?.message || error.message || '发送验证码失败';\r\n      message.error(errorMessage);\r\n    }\r\n  };\r\n\r\n  const handleSendSmsCode = async () => {\r\n    try {\r\n      const phone = phoneForm.getFieldValue('phone');\r\n      if (!phone) {\r\n        message.error('请输入手机号');\r\n        return;\r\n      }\r\n\r\n      const response = await authService.sendSmsVerification(phone);\r\n      if (response.success) {\r\n        message.success('验证码已发送');\r\n        setCountdown(60);\r\n        const timer = setInterval(() => {\r\n          setCountdown((prev) => {\r\n            if (prev <= 1) {\r\n              clearInterval(timer);\r\n              return 0;\r\n            }\r\n            return prev - 1;\r\n          });\r\n        }, 1000);\r\n      } else {\r\n        message.error(response.message || '发送验证码失败');\r\n      }\r\n    } catch (error: any) {\r\n      console.error('发送短信验证码错误:', error);\r\n      const errorMessage = error.response?.data?.message || error.message || '发送验证码失败';\r\n      message.error(errorMessage);\r\n    }\r\n  };\r\n\r\n  const handleWechatRegister = () => {\r\n    // 这里需要集成微信登录SDK\r\n    message.info('微信注册功能开发中...');\r\n  };\r\n\r\n  const handleAlipayRegister = () => {\r\n    // 这里需要集成支付宝登录SDK\r\n    message.info('支付宝注册功能开发中...');\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card\">\r\n        <div className=\"auth-header\">\r\n          <h1>创建账户</h1>\r\n          <p>加入家庭外卖系统，享受便捷服务</p>\r\n        </div>\r\n\r\n        <Tabs defaultActiveKey=\"email\" centered>\r\n          <TabPane tab=\"邮箱注册\" key=\"email\">\r\n            <Form\r\n              form={emailForm}\r\n              name=\"email_register\"\r\n              onFinish={handleEmailRegister}\r\n              autoComplete=\"off\"\r\n            >\r\n              <Form.Item\r\n                name=\"username\"\r\n                rules={[\r\n                  { required: true, message: '请输入用户名' },\r\n                  { min: 2, message: '用户名长度不能少于2位' }\r\n                ]}\r\n              >\r\n                <Input\r\n                  prefix={<UserOutlined />}\r\n                  placeholder=\"请输入用户名\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"email\"\r\n                rules={[\r\n                  { required: true, message: '请输入邮箱' },\r\n                  { type: 'email', message: '请输入有效的邮箱地址' }\r\n                ]}\r\n              >\r\n                <Input\r\n                  prefix={<MailOutlined />}\r\n                  placeholder=\"请输入邮箱\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                rules={[\r\n                  { required: true, message: '请输入密码' },\r\n                  { min: 6, message: '密码长度不能少于6位' }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  prefix={<LockOutlined />}\r\n                  placeholder=\"请输入密码\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: '请确认密码' },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('两次输入的密码不一致'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  prefix={<LockOutlined />}\r\n                  placeholder=\"请确认密码\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <Button\r\n                  type=\"primary\"\r\n                  htmlType=\"submit\"\r\n                  size=\"large\"\r\n                  loading={loading}\r\n                  className=\"submit-btn\"\r\n                >\r\n                  注册\r\n                </Button>\r\n              </Form.Item>\r\n            </Form>\r\n          </TabPane>\r\n\r\n          <TabPane tab=\"手机号注册\" key=\"phone\">\r\n            <Form\r\n              form={phoneForm}\r\n              name=\"phone_register\"\r\n              onFinish={handlePhoneRegister}\r\n              autoComplete=\"off\"\r\n            >\r\n              <Form.Item\r\n                name=\"username\"\r\n                rules={[\r\n                  { required: true, message: '请输入用户名' },\r\n                  { min: 2, message: '用户名长度不能少于2位' }\r\n                ]}\r\n              >\r\n                <Input\r\n                  prefix={<UserOutlined />}\r\n                  placeholder=\"请输入用户名\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phone\"\r\n                rules={[\r\n                  { required: true, message: '请输入手机号' },\r\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号' }\r\n                ]}\r\n              >\r\n                <Input\r\n                  prefix={<PhoneOutlined />}\r\n                  placeholder=\"请输入手机号\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                rules={[\r\n                  { required: true, message: '请输入密码' },\r\n                  { min: 6, message: '密码长度不能少于6位' }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  prefix={<LockOutlined />}\r\n                  placeholder=\"请输入密码\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: '请确认密码' },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('两次输入的密码不一致'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  prefix={<LockOutlined />}\r\n                  placeholder=\"请确认密码\"\r\n                  size=\"large\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <Button\r\n                  type=\"primary\"\r\n                  htmlType=\"submit\"\r\n                  size=\"large\"\r\n                  loading={loading}\r\n                  className=\"submit-btn\"\r\n                >\r\n                  注册\r\n                </Button>\r\n              </Form.Item>\r\n            </Form>\r\n          </TabPane>\r\n        </Tabs>\r\n\r\n        <Divider>\r\n          <span>或使用第三方注册</span>\r\n        </Divider>\r\n\r\n        <div className=\"social-login-buttons\">\r\n          <Button\r\n            icon={<WechatOutlined />}\r\n            className=\"wechat-btn\"\r\n            size=\"large\"\r\n            onClick={handleWechatRegister}\r\n          >\r\n            微信注册\r\n          </Button>\r\n          <Button\r\n            icon={<AlipayOutlined />}\r\n            className=\"alipay-btn\"\r\n            size=\"large\"\r\n            onClick={handleAlipayRegister}\r\n          >\r\n            支付宝注册\r\n          </Button>\r\n        </div>\r\n\r\n        <div className=\"switch-form\">\r\n          <span>已有账户？</span>\r\n          <Link to=\"/login\">立即登录</Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RegisterPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,OAAO,QAAQ,MAAM;AAClE,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAC3H,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAM;EAAEC;AAAQ,CAAC,GAAGf,IAAI;AAExB,MAAMgB,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,SAAS,CAAC,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;EAClC,MAAM,CAACC,SAAS,CAAC,GAAGvB,IAAI,CAACsB,OAAO,CAAC,CAAC;EAClC,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM;IAAE6B;EAAS,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,mBAAmB,GAAG,MAAOC,MAAW,IAAK;IACjD,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMO,OAAO,GAAG,MAAMJ,QAAQ,CAAC,OAAO,EAAEG,MAAM,CAAC;MAC/C,IAAIC,OAAO,EAAE;QACXH,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,mBAAmB,GAAG,MAAOH,MAAW,IAAK;IACjD,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMO,OAAO,GAAG,MAAMJ,QAAQ,CAAC,OAAO,EAAEG,MAAM,CAAC;MAC/C,IAAIC,OAAO,EAAE;QACXH,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAAC,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,KAAK,GAAGf,SAAS,CAACgB,aAAa,CAAC,OAAO,CAAC;MAC9C,IAAI,CAACD,KAAK,EAAE;QACVhC,OAAO,CAAC6B,KAAK,CAAC,OAAO,CAAC;QACtB;MACF;MAEA,MAAMK,QAAQ,GAAG,MAAMvB,WAAW,CAACwB,qBAAqB,CAACH,KAAK,CAAC;MAC/D,IAAIE,QAAQ,CAACN,OAAO,EAAE;QACpB5B,OAAO,CAAC4B,OAAO,CAAC,WAAW,CAAC;QAC5BL,YAAY,CAAC,EAAE,CAAC;QAChB,MAAMa,KAAK,GAAGC,WAAW,CAAC,MAAM;UAC9Bd,YAAY,CAAEe,IAAI,IAAK;YACrB,IAAIA,IAAI,IAAI,CAAC,EAAE;cACbC,aAAa,CAACH,KAAK,CAAC;cACpB,OAAO,CAAC;YACV;YACA,OAAOE,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLtC,OAAO,CAAC6B,KAAK,CAACK,QAAQ,CAAClC,OAAO,IAAI,SAAS,CAAC;MAC9C;IACF,CAAC,CAAC,OAAO6B,KAAU,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACnBC,OAAO,CAACb,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAMc,YAAY,GAAG,EAAAH,eAAA,GAAAX,KAAK,CAACK,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBzC,OAAO,KAAI6B,KAAK,CAAC7B,OAAO,IAAI,SAAS;MAChFA,OAAO,CAAC6B,KAAK,CAACc,YAAY,CAAC;IAC7B;EACF,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,KAAK,GAAG3B,SAAS,CAACc,aAAa,CAAC,OAAO,CAAC;MAC9C,IAAI,CAACa,KAAK,EAAE;QACV9C,OAAO,CAAC6B,KAAK,CAAC,QAAQ,CAAC;QACvB;MACF;MAEA,MAAMK,QAAQ,GAAG,MAAMvB,WAAW,CAACoC,mBAAmB,CAACD,KAAK,CAAC;MAC7D,IAAIZ,QAAQ,CAACN,OAAO,EAAE;QACpB5B,OAAO,CAAC4B,OAAO,CAAC,QAAQ,CAAC;QACzBL,YAAY,CAAC,EAAE,CAAC;QAChB,MAAMa,KAAK,GAAGC,WAAW,CAAC,MAAM;UAC9Bd,YAAY,CAAEe,IAAI,IAAK;YACrB,IAAIA,IAAI,IAAI,CAAC,EAAE;cACbC,aAAa,CAACH,KAAK,CAAC;cACpB,OAAO,CAAC;YACV;YACA,OAAOE,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLtC,OAAO,CAAC6B,KAAK,CAACK,QAAQ,CAAClC,OAAO,IAAI,SAAS,CAAC;MAC9C;IACF,CAAC,CAAC,OAAO6B,KAAU,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACnBP,OAAO,CAACb,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAMc,YAAY,GAAG,EAAAK,gBAAA,GAAAnB,KAAK,CAACK,QAAQ,cAAAc,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBjD,OAAO,KAAI6B,KAAK,CAAC7B,OAAO,IAAI,SAAS;MAChFA,OAAO,CAAC6B,KAAK,CAACc,YAAY,CAAC;IAC7B;EACF,CAAC;EAED,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAlD,OAAO,CAACmD,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACApD,OAAO,CAACmD,IAAI,CAAC,eAAe,CAAC;EAC/B,CAAC;EAED,oBACEtC,OAAA;IAAKwC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BzC,OAAA;MAAKwC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBzC,OAAA;QAAKwC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzC,OAAA;UAAAyC,QAAA,EAAI;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACb7C,OAAA;UAAAyC,QAAA,EAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAEN7C,OAAA,CAACd,IAAI;QAAC4D,gBAAgB,EAAC,OAAO;QAACC,QAAQ;QAAAN,QAAA,gBACrCzC,OAAA,CAACC,OAAO;UAAC+C,GAAG,EAAC,0BAAM;UAAAP,QAAA,eACjBzC,OAAA,CAACjB,IAAI;YACHkE,IAAI,EAAE7C,SAAU;YAChB8C,IAAI,EAAC,gBAAgB;YACrBC,QAAQ,EAAEtC,mBAAoB;YAC9BuC,YAAY,EAAC,KAAK;YAAAX,QAAA,gBAElBzC,OAAA,CAACjB,IAAI,CAACsE,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpE,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAEqE,GAAG,EAAE,CAAC;gBAAErE,OAAO,EAAE;cAAc,CAAC,CAClC;cAAAsD,QAAA,eAEFzC,OAAA,CAAChB,KAAK;gBACJyE,MAAM,eAAEzD,OAAA,CAACX,YAAY;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBa,WAAW,EAAC,sCAAQ;gBACpBC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7C,OAAA,CAACjB,IAAI,CAACsE,IAAI;cACRH,IAAI,EAAC,OAAO;cACZI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpE,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAEyE,IAAI,EAAE,OAAO;gBAAEzE,OAAO,EAAE;cAAa,CAAC,CACxC;cAAAsD,QAAA,eAEFzC,OAAA,CAAChB,KAAK;gBACJyE,MAAM,eAAEzD,OAAA,CAACT,YAAY;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBa,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7C,OAAA,CAACjB,IAAI,CAACsE,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpE,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAEqE,GAAG,EAAE,CAAC;gBAAErE,OAAO,EAAE;cAAa,CAAC,CACjC;cAAAsD,QAAA,eAEFzC,OAAA,CAAChB,KAAK,CAAC6E,QAAQ;gBACbJ,MAAM,eAAEzD,OAAA,CAACV,YAAY;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBa,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7C,OAAA,CAACjB,IAAI,CAACsE,IAAI;cACRH,IAAI,EAAC,iBAAiB;cACtBY,YAAY,EAAE,CAAC,UAAU,CAAE;cAC3BR,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpE,OAAO,EAAE;cAAQ,CAAC,EACpC,CAAC;gBAAEiC;cAAc,CAAC,MAAM;gBACtB2C,SAASA,CAACC,CAAC,EAAEC,KAAK,EAAE;kBAClB,IAAI,CAACA,KAAK,IAAI7C,aAAa,CAAC,UAAU,CAAC,KAAK6C,KAAK,EAAE;oBACjD,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;kBAC1B;kBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChD;cACF,CAAC,CAAC,CACF;cAAA5B,QAAA,eAEFzC,OAAA,CAAChB,KAAK,CAAC6E,QAAQ;gBACbJ,MAAM,eAAEzD,OAAA,CAACV,YAAY;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBa,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7C,OAAA,CAACjB,IAAI,CAACsE,IAAI;cAAAZ,QAAA,eACRzC,OAAA,CAACf,MAAM;gBACL2E,IAAI,EAAC,SAAS;gBACdU,QAAQ,EAAC,QAAQ;gBACjBX,IAAI,EAAC,OAAO;gBACZpD,OAAO,EAAEA,OAAQ;gBACjBiC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACvB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC,GAlFe,OAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmFtB,CAAC,eAEV7C,OAAA,CAACC,OAAO;UAAC+C,GAAG,EAAC,gCAAO;UAAAP,QAAA,eAClBzC,OAAA,CAACjB,IAAI;YACHkE,IAAI,EAAE3C,SAAU;YAChB4C,IAAI,EAAC,gBAAgB;YACrBC,QAAQ,EAAElC,mBAAoB;YAC9BmC,YAAY,EAAC,KAAK;YAAAX,QAAA,gBAElBzC,OAAA,CAACjB,IAAI,CAACsE,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpE,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAEqE,GAAG,EAAE,CAAC;gBAAErE,OAAO,EAAE;cAAc,CAAC,CAClC;cAAAsD,QAAA,eAEFzC,OAAA,CAAChB,KAAK;gBACJyE,MAAM,eAAEzD,OAAA,CAACX,YAAY;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBa,WAAW,EAAC,sCAAQ;gBACpBC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7C,OAAA,CAACjB,IAAI,CAACsE,IAAI;cACRH,IAAI,EAAC,OAAO;cACZI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpE,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAEoF,OAAO,EAAE,eAAe;gBAAEpF,OAAO,EAAE;cAAY,CAAC,CAClD;cAAAsD,QAAA,eAEFzC,OAAA,CAAChB,KAAK;gBACJyE,MAAM,eAAEzD,OAAA,CAACR,aAAa;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1Ba,WAAW,EAAC,sCAAQ;gBACpBC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7C,OAAA,CAACjB,IAAI,CAACsE,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpE,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAEqE,GAAG,EAAE,CAAC;gBAAErE,OAAO,EAAE;cAAa,CAAC,CACjC;cAAAsD,QAAA,eAEFzC,OAAA,CAAChB,KAAK,CAAC6E,QAAQ;gBACbJ,MAAM,eAAEzD,OAAA,CAACV,YAAY;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBa,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7C,OAAA,CAACjB,IAAI,CAACsE,IAAI;cACRH,IAAI,EAAC,iBAAiB;cACtBY,YAAY,EAAE,CAAC,UAAU,CAAE;cAC3BR,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpE,OAAO,EAAE;cAAQ,CAAC,EACpC,CAAC;gBAAEiC;cAAc,CAAC,MAAM;gBACtB2C,SAASA,CAACC,CAAC,EAAEC,KAAK,EAAE;kBAClB,IAAI,CAACA,KAAK,IAAI7C,aAAa,CAAC,UAAU,CAAC,KAAK6C,KAAK,EAAE;oBACjD,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;kBAC1B;kBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChD;cACF,CAAC,CAAC,CACF;cAAA5B,QAAA,eAEFzC,OAAA,CAAChB,KAAK,CAAC6E,QAAQ;gBACbJ,MAAM,eAAEzD,OAAA,CAACV,YAAY;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBa,WAAW,EAAC,gCAAO;gBACnBC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7C,OAAA,CAACjB,IAAI,CAACsE,IAAI;cAAAZ,QAAA,eACRzC,OAAA,CAACf,MAAM;gBACL2E,IAAI,EAAC,SAAS;gBACdU,QAAQ,EAAC,QAAQ;gBACjBX,IAAI,EAAC,OAAO;gBACZpD,OAAO,EAAEA,OAAQ;gBACjBiC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACvB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC,GAlFgB,OAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmFvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEP7C,OAAA,CAACZ,OAAO;QAAAqD,QAAA,eACNzC,OAAA;UAAAyC,QAAA,EAAM;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAEV7C,OAAA;QAAKwC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCzC,OAAA,CAACf,MAAM;UACLuF,IAAI,eAAExE,OAAA,CAACP,cAAc;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBL,SAAS,EAAC,YAAY;UACtBmB,IAAI,EAAC,OAAO;UACZc,OAAO,EAAEpC,oBAAqB;UAAAI,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7C,OAAA,CAACf,MAAM;UACLuF,IAAI,eAAExE,OAAA,CAACN,cAAc;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBL,SAAS,EAAC,YAAY;UACtBmB,IAAI,EAAC,OAAO;UACZc,OAAO,EAAElC,oBAAqB;UAAAE,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzC,OAAA;UAAAyC,QAAA,EAAM;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClB7C,OAAA,CAACL,IAAI;UAAC+E,EAAE,EAAC,QAAQ;UAAAjC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA9TID,YAAsB;EAAA,QACNnB,IAAI,CAACsB,OAAO,EACZtB,IAAI,CAACsB,OAAO,EAGXR,OAAO,EACXD,WAAW;AAAA;AAAA+E,EAAA,GANxBzE,YAAsB;AAgU5B,eAAeA,YAAY;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}