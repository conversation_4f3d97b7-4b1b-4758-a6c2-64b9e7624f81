import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './contexts/AuthContext';
import { CartProvider } from './contexts/CartContext';
import { AddressProvider } from './contexts/AddressContext';
import { OrderProvider } from './contexts/OrderContext';
import { FavoriteProvider } from './contexts/FavoriteContext';
import PrivateRoute from './components/PrivateRoute';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';
import ProfilePage from './pages/ProfilePage';
import AddressPage from './pages/AddressPage';
import OrderPage from './pages/OrderPage';
import ProductsPage from './pages/ProductsPage';
import ProductDetailPage from './pages/ProductDetailPage';
import CartPage from './pages/CartPage';
import CheckoutPage from './pages/CheckoutPage';
import PaymentPage from './pages/PaymentPage';
import FavoritePage from './pages/FavoritePage';
import MerchantRefundPage from './pages/MerchantRefundPage';
import './App.css';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <AddressProvider>
          <CartProvider>
            <OrderProvider>
              <FavoriteProvider>
                <Router>
          <div className="App">
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/dashboard" element={
                <PrivateRoute>
                  <DashboardPage />
                </PrivateRoute>
              } />
              <Route path="/profile" element={
                <PrivateRoute>
                  <ProfilePage />
                </PrivateRoute>
              } />
              <Route path="/addresses" element={
                <PrivateRoute>
                  <AddressPage />
                </PrivateRoute>
              } />
              <Route path="/orders" element={
                <PrivateRoute>
                  <OrderPage />
                </PrivateRoute>
              } />
              <Route path="/products" element={
                <PrivateRoute>
                  <ProductsPage />
                </PrivateRoute>
              } />
              <Route path="/products/:id" element={
                <PrivateRoute>
                  <ProductDetailPage />
                </PrivateRoute>
              } />
              <Route path="/cart" element={
                <PrivateRoute>
                  <CartPage />
                </PrivateRoute>
              } />
              <Route path="/checkout" element={
                <PrivateRoute>
                  <CheckoutPage />
                </PrivateRoute>
              } />
              <Route path="/payment" element={
                <PrivateRoute>
                  <PaymentPage />
                </PrivateRoute>
              } />
              <Route path="/favorites" element={
                <PrivateRoute>
                  <FavoritePage />
                </PrivateRoute>
              } />
              <Route path="/merchant/refunds" element={
                <PrivateRoute>
                  <MerchantRefundPage />
                </PrivateRoute>
              } />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </div>
                </Router>
              </FavoriteProvider>
            </OrderProvider>
          </CartProvider>
        </AddressProvider>
      </AuthProvider>
    </ConfigProvider>
  );
};

export default App;