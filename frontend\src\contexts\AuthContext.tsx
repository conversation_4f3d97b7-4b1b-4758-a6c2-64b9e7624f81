import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { message } from 'antd';
import { authService } from '../services/authService';

interface User {
  id: number;
  username: string;
  email?: string;
  phone?: string;
  nickname?: string;
  avatar?: string;
  gender?: string;
  birth_date?: string;
  is_active: boolean;
  is_verified: boolean;
  email_verified: boolean;
  phone_verified: boolean;
  created_at: string;
  last_login?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (type: string, credentials: any) => Promise<boolean>;
  register: (type: string, credentials: any) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 检查本地存储的token和用户信息
    const token = localStorage.getItem('access_token');
    const userData = localStorage.getItem('user');
    
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        
        // 验证token是否有效
        authService.getProfile()
          .then(userInfo => {
            setUser(userInfo);
            localStorage.setItem('user', JSON.stringify(userInfo));
          })
          .catch(() => {
            // token无效，清除本地存储
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user');
            setUser(null);
          })
          .finally(() => {
            setLoading(false);
          });
      } catch (error) {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        setUser(null);
        setLoading(false);
      }
    } else {
      setLoading(false);
    }
  }, []);

  const login = async (type: string, credentials: any): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authService.login(type, credentials);

      if (response.success) {
        // 处理不同的响应结构
        const token = response.access_token || response.data?.token;
        const refreshToken = response.refresh_token || response.data?.refresh_token;
        const userData = response.user || response.data?.user;

        if (token) {
          localStorage.setItem('access_token', token);
        }
        if (refreshToken) {
          localStorage.setItem('refresh_token', refreshToken);
        }
        if (userData) {
          localStorage.setItem('user', JSON.stringify(userData));
          setUser(userData);
        }

        message.success(response.message || '登录成功');
        return true;
      } else {
        message.error(response.message || '登录失败');
        return false;
      }
    } catch (error: any) {
      console.error('登录错误:', error);
      const errorMessage = error.response?.data?.message || error.message || '登录失败，请稍后重试';
      message.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const register = async (type: string, credentials: any): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authService.register(type, credentials);
      
      if (response.success) {
        message.success(response.message || '注册成功');
        return true;
      } else {
        message.error(response.message || '注册失败');
        return false;
      }
    } catch (error: any) {
      console.error('注册错误:', error);
      const errorMessage = error.response?.data?.message || error.message || '注册失败，请稍后重试';
      message.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    setUser(null);
    message.success('已退出登录');
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 