import React, { useState } from 'react';
import { Form, Input, Button, Tabs, message, Divider } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, WechatOutlined, AlipayOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/authService';

const { TabPane } = Tabs;

const LoginPage: React.FC = () => {
  const [emailForm] = Form.useForm();
  const [phoneForm] = Form.useForm();
  const [verificationForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleLogin = async (values: any) => {
    try {
      setLoading(true);
      const success = await login('email', values);
      if (success) {
        navigate('/dashboard');
      }
    } catch (error) {
      message.error('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneLogin = async (values: any) => {
    try {
      setLoading(true);
      const success = await login('phone', values);
      if (success) {
        navigate('/dashboard');
      }
    } catch (error) {
      message.error('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationCodeLogin = async (values: any) => {
    try {
      setLoading(true);
      const success = await login('verification_code', values);
      if (success) {
        navigate('/dashboard');
      }
    } catch (error) {
      message.error('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSendCode = async () => {
    try {
      const phone = verificationForm.getFieldValue('phone');
      if (!phone) {
        message.error('请输入手机号');
        return;
      }

      const response = await authService.sendSmsVerification(phone);
      if (response.success) {
        message.success('验证码已发送');
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(response.message || '发送验证码失败');
      }
    } catch (error: any) {
      console.error('发送验证码错误:', error);
      const errorMessage = error.response?.data?.message || error.message || '发送验证码失败';
      message.error(errorMessage);
    }
  };

  const handleWechatLogin = () => {
    // 这里需要集成微信登录SDK
    message.info('微信登录功能开发中...');
  };

  const handleAlipayLogin = () => {
    // 这里需要集成支付宝登录SDK
    message.info('支付宝登录功能开发中...');
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1>欢迎回来</h1>
          <p>登录您的家庭外卖账户</p>
        </div>

        <Tabs defaultActiveKey="email" centered>
          <TabPane tab="邮箱登录" key="email">
            <Form
              form={emailForm}
              name="email_login"
              onFinish={handleLogin}
              autoComplete="off"
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="请输入邮箱"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码长度不能少于6位' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  loading={loading}
                  className="submit-btn"
                >
                  登录
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="手机号登录" key="phone">
            <Form
              form={phoneForm}
              name="phone_login"
              onFinish={handlePhoneLogin}
              autoComplete="off"
            >
              <Form.Item
                name="phone"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="请输入手机号"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码长度不能少于6位' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  loading={loading}
                  className="submit-btn"
                >
                  登录
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="验证码登录" key="verification">
            <Form
              form={verificationForm}
              name="verification_login"
              onFinish={handleVerificationCodeLogin}
              autoComplete="off"
            >
              <Form.Item
                name="phone"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="请输入手机号"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="code"
                rules={[
                  { required: true, message: '请输入验证码' },
                  { len: 6, message: '验证码为6位数字' }
                ]}
              >
                <div className="verification-code-input">
                  <Input
                    placeholder="请输入验证码"
                    size="large"
                    maxLength={6}
                  />
                  <Button
                    type="primary"
                    onClick={handleSendCode}
                    disabled={countdown > 0}
                    className="send-code-btn"
                  >
                    {countdown > 0 ? `${countdown}s` : '发送验证码'}
                  </Button>
                </div>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  loading={loading}
                  className="submit-btn"
                >
                  登录
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>

        <Divider>
          <span>或使用第三方登录</span>
        </Divider>

        <div className="social-login-buttons">
          <Button
            icon={<WechatOutlined />}
            className="wechat-btn"
            size="large"
            onClick={handleWechatLogin}
          >
            微信登录
          </Button>
          <Button
            icon={<AlipayOutlined />}
            className="alipay-btn"
            size="large"
            onClick={handleAlipayLogin}
          >
            支付宝登录
          </Button>
        </div>

        <div className="switch-form">
          <span>还没有账户？</span>
          <Link to="/register">立即注册</Link>
        </div>
      </div>
    </div>
  );
};

export default LoginPage; 