import React, { useState } from 'react';
import { Form, Input, Button, Tabs, message, Divider } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, WechatOutlined, AlipayOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/authService';

const { TabPane } = Tabs;

const RegisterPage: React.FC = () => {
  const [emailForm] = Form.useForm();
  const [phoneForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const { register } = useAuth();
  const navigate = useNavigate();

  const handleEmailRegister = async (values: any) => {
    try {
      setLoading(true);
      const success = await register('email', values);
      if (success) {
        navigate('/login');
      }
    } catch (error) {
      message.error('注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneRegister = async (values: any) => {
    try {
      setLoading(true);
      const success = await register('phone', values);
      if (success) {
        navigate('/login');
      }
    } catch (error) {
      message.error('注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSendEmailCode = async () => {
    try {
      const email = emailForm.getFieldValue('email');
      if (!email) {
        message.error('请输入邮箱');
        return;
      }

      const response = await authService.sendEmailVerification(email);
      if (response.success) {
        message.success('验证码已发送到邮箱');
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(response.message || '发送验证码失败');
      }
    } catch (error: any) {
      console.error('发送邮箱验证码错误:', error);
      const errorMessage = error.response?.data?.message || error.message || '发送验证码失败';
      message.error(errorMessage);
    }
  };

  const handleSendSmsCode = async () => {
    try {
      const phone = phoneForm.getFieldValue('phone');
      if (!phone) {
        message.error('请输入手机号');
        return;
      }

      const response = await authService.sendSmsVerification(phone);
      if (response.success) {
        message.success('验证码已发送');
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(response.message || '发送验证码失败');
      }
    } catch (error: any) {
      console.error('发送短信验证码错误:', error);
      const errorMessage = error.response?.data?.message || error.message || '发送验证码失败';
      message.error(errorMessage);
    }
  };

  const handleWechatRegister = () => {
    // 这里需要集成微信登录SDK
    message.info('微信注册功能开发中...');
  };

  const handleAlipayRegister = () => {
    // 这里需要集成支付宝登录SDK
    message.info('支付宝注册功能开发中...');
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1>创建账户</h1>
          <p>加入家庭外卖系统，享受便捷服务</p>
        </div>

        <Tabs defaultActiveKey="email" centered>
          <TabPane tab="邮箱注册" key="email">
            <Form
              form={emailForm}
              name="email_register"
              onFinish={handleEmailRegister}
              autoComplete="off"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 2, message: '用户名长度不能少于2位' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="请输入邮箱"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码长度不能少于6位' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请确认密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  loading={loading}
                  className="submit-btn"
                >
                  注册
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="手机号注册" key="phone">
            <Form
              form={phoneForm}
              name="phone_register"
              onFinish={handlePhoneRegister}
              autoComplete="off"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 2, message: '用户名长度不能少于2位' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="phone"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="请输入手机号"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码长度不能少于6位' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请确认密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  loading={loading}
                  className="submit-btn"
                >
                  注册
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>

        <Divider>
          <span>或使用第三方注册</span>
        </Divider>

        <div className="social-login-buttons">
          <Button
            icon={<WechatOutlined />}
            className="wechat-btn"
            size="large"
            onClick={handleWechatRegister}
          >
            微信注册
          </Button>
          <Button
            icon={<AlipayOutlined />}
            className="alipay-btn"
            size="large"
            onClick={handleAlipayRegister}
          >
            支付宝注册
          </Button>
        </div>

        <div className="switch-form">
          <span>已有账户？</span>
          <Link to="/login">立即登录</Link>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage; 