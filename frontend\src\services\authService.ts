import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理token过期
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken,
          });
          
          const { access_token } = response.data;
          localStorage.setItem('access_token', access_token);
          
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return api(originalRequest);
        } catch (refreshError) {
          // 刷新token失败，清除本地存储
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('user');
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      }
    }
    
    return Promise.reject(error);
  }
);

export const authService = {
  // 邮箱注册
  registerWithEmail: async (email: string, password: string, username?: string) => {
    try {
      const response = await api.post('/auth/register/email', {
        email,
        password,
        username
      });
      return response.data;
    } catch (error: any) {
      // 如果后端不可用，使用本地存储模拟
      console.warn('后端不可用，使用本地存储模拟注册');

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 检查邮箱是否已存在（模拟）
      const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');
      const emailExists = existingUsers.some((user: any) => user.email === email);

      if (emailExists) {
        return {
          success: false,
          message: '该邮箱已被注册，请使用其他邮箱'
        };
      }

      // 创建新用户 - 使用简单的哈希模拟（实际项目中应该使用bcrypt等）
      const newUser = {
        id: Date.now(),
        username: username || email.split('@')[0],
        email,
        passwordHash: btoa(password), // 简单的base64编码，仅用于演示
        avatar: null,
        phone: null,
        createdAt: new Date().toISOString()
      };

      // 保存到本地存储
      existingUsers.push(newUser);
      localStorage.setItem('users', JSON.stringify(existingUsers));

      return {
        success: true,
        message: '注册成功！请登录'
      };
    }
  },

  // 手机号注册
  registerWithPhone: async (phone: string, password: string, username?: string) => {
    try {
      const response = await api.post('/auth/register/phone', {
        phone,
        password,
        username
      });
      return response.data;
    } catch (error: any) {
      // 如果后端不可用，使用本地存储模拟
      console.warn('后端不可用，使用本地存储模拟注册');

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 检查手机号是否已存在（模拟）
      const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');
      const phoneExists = existingUsers.some((user: any) => user.phone === phone);

      if (phoneExists) {
        return {
          success: false,
          message: '该手机号已被注册，请使用其他手机号'
        };
      }

      // 创建新用户 - 使用简单的哈希模拟
      const newUser = {
        id: Date.now(),
        username: username || `用户${phone.slice(-4)}`,
        phone,
        passwordHash: btoa(password), // 简单的base64编码，仅用于演示
        avatar: null,
        email: null,
        createdAt: new Date().toISOString()
      };

      // 保存到本地存储
      existingUsers.push(newUser);
      localStorage.setItem('users', JSON.stringify(existingUsers));

      return {
        success: true,
        message: '注册成功！请登录'
      };
    }
  },

  // 邮箱登录
  loginWithEmail: async (email: string, password: string) => {
    try {
      const response = await api.post('/auth/login/email', {
        email,
        password
      });
      return response.data;
    } catch (error: any) {
      // 如果后端不可用，使用本地存储模拟
      console.warn('后端不可用，使用本地存储模拟登录');

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      // 从本地存储获取用户数据
      const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');
      const user = existingUsers.find((u: any) => u.email === email && u.passwordHash === btoa(password));

      if (user) {
        return {
          success: true,
          message: '登录成功',
          data: {
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              phone: user.phone,
              avatar: user.avatar
            },
            token: `mock_token_${user.id}_${Date.now()}`,
            refresh_token: `mock_refresh_${user.id}_${Date.now()}`
          }
        };
      } else {
        return {
          success: false,
          message: '邮箱或密码错误'
        };
      }
    }
  },

  // 手机号登录
  loginWithPhone: async (phone: string, password: string) => {
    try {
      const response = await api.post('/auth/login/phone', {
        phone,
        password
      });
      return response.data;
    } catch (error: any) {
      // 如果后端不可用，使用本地存储模拟
      console.warn('后端不可用，使用本地存储模拟登录');

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      // 从本地存储获取用户数据
      const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');
      const user = existingUsers.find((u: any) => u.phone === phone && u.passwordHash === btoa(password));

      if (user) {
        return {
          success: true,
          message: '登录成功',
          data: {
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              phone: user.phone,
              avatar: user.avatar
            },
            token: `mock_token_${user.id}_${Date.now()}`,
            refresh_token: `mock_refresh_${user.id}_${Date.now()}`
          }
        };
      } else {
        return {
          success: false,
          message: '手机号或密码错误'
        };
      }
    }
  },

  // 验证码登录
  loginWithVerificationCode: async (phone: string, code: string) => {
    try {
      const response = await api.post('/auth/login/verification-code', {
        phone,
        code
      });
      return response.data;
    } catch (error: any) {
      // 如果后端不可用，使用本地存储模拟
      console.warn('后端不可用，使用本地存储模拟验证码登录');

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      // 模拟验证码验证（简单模拟，实际项目中需要真实验证）
      if (code !== '123456') {
        return {
          success: false,
          message: '验证码错误'
        };
      }

      // 从本地存储获取用户数据
      const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');
      const user = existingUsers.find((u: any) => u.phone === phone);

      if (user) {
        return {
          success: true,
          message: '登录成功',
          data: {
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              phone: user.phone,
              avatar: user.avatar
            },
            token: `mock_token_${user.id}_${Date.now()}`,
            refresh_token: `mock_refresh_${user.id}_${Date.now()}`
          }
        };
      } else {
        // 如果用户不存在，自动注册
        const newUser = {
          id: Date.now(),
          username: `用户${phone.slice(-4)}`,
          phone,
          passwordHash: null,
          avatar: null,
          email: null,
          createdAt: new Date().toISOString()
        };

        existingUsers.push(newUser);
        localStorage.setItem('users', JSON.stringify(existingUsers));

        return {
          success: true,
          message: '登录成功',
          data: {
            user: {
              id: newUser.id,
              username: newUser.username,
              email: newUser.email,
              phone: newUser.phone,
              avatar: newUser.avatar
            },
            token: `mock_token_${newUser.id}_${Date.now()}`,
            refresh_token: `mock_refresh_${newUser.id}_${Date.now()}`
          }
        };
      }
    }
  },

  // 微信登录
  loginWithWechat: async (code: string) => {
    try {
      const response = await api.post('/auth/login/wechat', {
        code
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '微信登录功能暂未开放'
      };
    }
  },



  // 支付宝登录
  loginWithAlipay: async (authCode: string) => {
    const response = await api.post('/auth/login/alipay', {
      auth_code: authCode,
    });
    return response.data;
  },

  // 发送邮箱验证码
  sendEmailVerification: async (email: string) => {
    try {
      const response = await api.post('/auth/send-email-code', {
        email,
      });
      return response.data;
    } catch (error: any) {
      // 如果后端不可用，模拟发送成功
      console.warn('后端不可用，模拟发送邮箱验证码');

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        message: '验证码已发送到邮箱（模拟）'
      };
    }
  },

  // 发送短信验证码
  sendSmsVerification: async (phone: string) => {
    try {
      const response = await api.post('/auth/send-sms-code', {
        phone,
      });
      return response.data;
    } catch (error: any) {
      // 如果后端不可用，模拟发送成功
      console.warn('后端不可用，模拟发送短信验证码');

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        message: '验证码已发送到手机（模拟，验证码为：123456）'
      };
    }
  },

  // 获取用户信息
  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data.user;
  },

  // 登出
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  // 通用登录方法
  login: async (type: string, credentials: any) => {
    switch (type) {
      case 'email':
        return await authService.loginWithEmail(credentials.email, credentials.password);
      case 'phone':
        return await authService.loginWithPhone(credentials.phone, credentials.password);
      case 'verification_code':
        return await authService.loginWithVerificationCode(credentials.phone, credentials.code);
      case 'wechat':
        return await authService.loginWithWechat(credentials.code);
      case 'alipay':
        return await authService.loginWithAlipay(credentials.authCode);
      default:
        throw new Error('不支持的登录类型');
    }
  },

  // 通用注册方法
  register: async (type: string, credentials: any) => {
    switch (type) {
      case 'email':
        return await authService.registerWithEmail(credentials.email, credentials.password, credentials.username);
      case 'phone':
        return await authService.registerWithPhone(credentials.phone, credentials.password, credentials.username);
      default:
        throw new Error('不支持的注册类型');
    }
  },
}; 