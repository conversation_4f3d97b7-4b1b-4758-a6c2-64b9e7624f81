<!DOCTYPE html>
<html>
<head>
    <title>测试注册功能</title>
</head>
<body>
    <h1>注册功能测试</h1>
    <p>请在浏览器控制台中运行以下代码来测试注册功能：</p>
    
    <pre>
// 测试手机号注册
const testPhoneRegister = async () => {
    const authService = {
        registerWithPhone: async (phone, password, username) => {
            await new Promise(resolve => setTimeout(resolve, 1000));
            const existingUsers = JSON.parse(localStorage.getItem('users') || '[]');
            const phoneExists = existingUsers.some(user => user.phone === phone);
            
            if (phoneExists) {
                return {
                    success: false,
                    message: '该手机号已被注册，请使用其他手机号'
                };
            }
            
            const newUser = {
                id: Date.now(),
                username: username || `用户${phone.slice(-4)}`,
                phone,
                password,
                avatar: null,
                email: null,
                createdAt: new Date().toISOString()
            };
            
            existingUsers.push(newUser);
            localStorage.setItem('users', JSON.stringify(existingUsers));
            
            return {
                success: true,
                message: '注册成功！请登录'
            };
        },
        
        register: async (type, credentials) => {
            if (type === 'phone') {
                return await authService.registerWithPhone(credentials.phone, credentials.password, credentials.username);
            }
            throw new Error('不支持的注册类型');
        }
    };
    
    // 测试注册
    const result = await authService.register('phone', {
        username: '测试用户',
        phone: '13800138000',
        password: '123456'
    });
    
    console.log('注册结果:', result);
    return result;
};

// 运行测试
testPhoneRegister().then(result => {
    console.log('测试完成:', result);
}).catch(error => {
    console.error('测试失败:', error);
});
    </pre>
    
    <p>如果注册功能正常，应该会在控制台看到成功的结果。</p>
</body>
</html>
