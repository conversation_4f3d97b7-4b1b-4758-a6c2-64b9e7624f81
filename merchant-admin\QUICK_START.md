# 🚀 商家管理系统 - 快速启动指南

## 📋 系统要求
- **Python** >= 3.8
- **Node.js** >= 16.0
- **Git** (可选)

## ⚡ 一键启动

### 方法1: 使用启动脚本 (推荐)

#### Windows用户
```bash
# 双击运行
start.bat
```

#### macOS/Linux用户
```bash
# 给脚本执行权限
chmod +x start.sh

# 运行启动脚本
./start.sh
```

### 方法2: 使用测试脚本
```bash
# 运行完整测试
python test_system.py
```

### 方法3: 手动启动

#### 1. 启动后端
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python -c "from app import db; db.create_all()"

# 启动后端
python app.py
```

#### 2. 启动前端
```bash
cd frontend

# 安装依赖
npm install

# 启动前端
npm start
```

## 🌐 访问系统

- **前端地址**: http://localhost:3000
- **后端地址**: http://localhost:5001
- **API健康检查**: http://localhost:5001/api/health

## 👤 演示账户

```
用户名: admin
密码: 123456
```

## 🎯 主要功能

### ✅ 已实现功能
- [x] 商家注册登录系统
- [x] 多步骤注册流程
- [x] 密码登录 + 验证码登录
- [x] 商家状态管理
- [x] 权限控制系统
- [x] 菜品分类管理
- [x] 商品管理 (CRUD)
- [x] 拖拽排序功能
- [x] 图片上传功能
- [x] 订单管理
- [x] 退款管理
- [x] 评价管理
- [x] 配送管理
- [x] 数据统计
- [x] 响应式设计

### 🔧 技术特色
- **前端**: React 18 + TypeScript + Ant Design
- **后端**: Flask + SQLAlchemy + JWT
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **认证**: JWT Token + 权限控制
- **文件上传**: 本地存储 + 云存储支持
- **拖拽功能**: React DnD
- **图表**: ECharts

## 🐛 常见问题

### 端口被占用
```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# macOS/Linux
lsof -ti:3000 | xargs kill -9
```

### Python模块未找到
```bash
# 确保虚拟环境已激活
pip install -r requirements.txt
```

### 数据库错误
```bash
# 重新初始化数据库
python -c "from app import db; db.drop_all(); db.create_all()"
```

### 前端依赖问题
```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install
```

## 📞 获取帮助

如果遇到问题：
1. 检查系统要求是否满足
2. 查看终端错误信息
3. 尝试重新安装依赖
4. 使用测试脚本诊断问题

## 🎉 开始使用

1. 运行启动脚本
2. 打开浏览器访问 http://localhost:3000
3. 使用演示账户登录: admin / 123456
4. 开始体验商家管理系统！

---

**祝您使用愉快！** 🎊
