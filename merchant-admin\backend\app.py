#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家管理系统后端主应用
Author: Merchant Admin System
Date: 2024-12-12
"""

import os
from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_cors import CORS
from datetime import timedelta
import logging
from logging.handlers import RotatingFileHandler

# 创建Flask应用
app = Flask(__name__)

# 配置
class Config:
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'merchant-admin-secret-key-2024'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///merchant_admin.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-2024'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 其他配置
    JSON_AS_ASCII = False
    JSONIFY_PRETTYPRINT_REGULAR = True

app.config.from_object(Config)

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'images'), exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'documents'), exist_ok=True)

# 初始化扩展
db = SQLAlchemy(app)
migrate = Migrate(app, db)
jwt = JWTManager(app)
CORS(app, origins=['http://localhost:3000'], supports_credentials=True)

# 配置日志
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')
    file_handler = RotatingFileHandler('logs/merchant_admin.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('商家管理系统启动')

# 导入模型（必须在db初始化之后）
try:
    from app.models.merchant import Merchant, MerchantUser, MerchantStatus, MerchantUserRole, BusinessType
    from app.models.product import Category, Product, ProductVariant, ProductStatus
    from app.models.order import Order, OrderItem, OrderStatus, PaymentMethod, DeliveryType
    from app.models.refund import OrderRefund, RefundItem, RefundStatus, RefundType, RefundReason
    from app.models.refund import CustomerService, ServiceMessage
    from app.models.delivery import DeliveryPerson, DeliveryArea, DeliveryTask, DeliveryTrack
    from app.models.delivery import DeliveryPersonStatus, DeliveryProvider
    from app.models.review import Review, ProductReview, ReviewType
    from app.models.notification import Notification, SystemMessage, NotificationType, NotificationPriority
except ImportError as e:
    app.logger.warning(f'模型导入失败: {str(e)}')

# 导入路由
try:
    from app.routes import auth, merchant, product, order, refund, review, delivery, analytics, upload
except ImportError as e:
    app.logger.warning(f'路由导入失败: {str(e)}')

# 注册蓝图
app.register_blueprint(auth.bp, url_prefix='/api/auth')
app.register_blueprint(merchant.bp, url_prefix='/api/merchant')
app.register_blueprint(product.bp, url_prefix='/api/products')
app.register_blueprint(order.bp, url_prefix='/api/orders')
app.register_blueprint(refund.bp, url_prefix='/api/refunds')
app.register_blueprint(review.bp, url_prefix='/api/reviews')
app.register_blueprint(delivery.bp, url_prefix='/api/delivery')
app.register_blueprint(analytics.bp, url_prefix='/api/analytics')
app.register_blueprint(upload.bp, url_prefix='/api/upload')

# 全局错误处理
@app.errorhandler(400)
def bad_request(error):
    return jsonify({
        'success': False,
        'message': '请求参数错误',
        'error': str(error)
    }), 400

@app.errorhandler(401)
def unauthorized(error):
    return jsonify({
        'success': False,
        'message': '未授权访问',
        'error': str(error)
    }), 401

@app.errorhandler(403)
def forbidden(error):
    return jsonify({
        'success': False,
        'message': '禁止访问',
        'error': str(error)
    }), 403

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': '资源不存在',
        'error': str(error)
    }), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return jsonify({
        'success': False,
        'message': '服务器内部错误',
        'error': str(error)
    }), 500

# JWT错误处理
@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({
        'success': False,
        'message': 'Token已过期',
        'error': 'token_expired'
    }), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    return jsonify({
        'success': False,
        'message': 'Token无效',
        'error': 'token_invalid'
    }), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({
        'success': False,
        'message': '缺少访问令牌',
        'error': 'token_missing'
    }), 401

# 健康检查接口
@app.route('/api/health')
def health_check():
    return jsonify({
        'success': True,
        'message': '服务正常运行',
        'data': {
            'status': 'healthy',
            'version': '1.0.0',
            'timestamp': db.func.now()
        }
    })

# 根路径
@app.route('/')
def index():
    return jsonify({
        'success': True,
        'message': '商家管理系统API',
        'data': {
            'name': 'Merchant Admin System API',
            'version': '1.0.0',
            'description': '专业的餐饮商家管理平台后端API',
            'endpoints': {
                'auth': '/api/auth',
                'merchant': '/api/merchant',
                'products': '/api/products',
                'orders': '/api/orders',
                'refunds': '/api/refunds',
                'reviews': '/api/reviews',
                'delivery': '/api/delivery',
                'analytics': '/api/analytics',
                'upload': '/api/upload',
                'health': '/api/health'
            }
        }
    })

# 创建数据库表
@app.before_first_request
def create_tables():
    """在第一次请求前创建数据库表"""
    try:
        db.create_all()
        app.logger.info('数据库表创建成功')
        
        # 创建默认管理员账户
        from app.models.merchant import Merchant, MerchantUser, MerchantUserRole, MerchantStatus, BusinessType
        from werkzeug.security import generate_password_hash
        
        # 检查是否已存在默认商家
        default_merchant = Merchant.query.filter_by(business_license='DEFAULT001').first()
        if not default_merchant:
            # 创建默认商家
            default_merchant = Merchant(
                name='演示餐厅',
                business_name='演示餐厅有限公司',
                business_license='DEFAULT001',
                contact_person='管理员',
                contact_phone='***********',
                email='<EMAIL>',
                province='广东省',
                city='深圳市',
                district='南山区',
                address='科技园南区',
                business_type=BusinessType.RESTAURANT,
                description='这是一个演示商家账户',
                status=MerchantStatus.APPROVED,
                is_active=True
            )
            db.session.add(default_merchant)
            db.session.flush()
            
            # 创建默认管理员用户
            admin_user = MerchantUser(
                merchant_id=default_merchant.id,
                username='admin',
                email='<EMAIL>',
                phone='***********',
                password_hash=generate_password_hash('123456'),
                role=MerchantUserRole.OWNER,
                permissions=['all'],
                is_active=True
            )
            db.session.add(admin_user)
            db.session.commit()
            app.logger.info('默认管理员账户创建成功: admin/123456')
            
    except Exception as e:
        app.logger.error(f'数据库初始化失败: {str(e)}')
        db.session.rollback()

if __name__ == '__main__':
    # 开发环境运行
    app.run(
        host='0.0.0.0',
        port=5001,
        debug=True,
        threaded=True
    )
