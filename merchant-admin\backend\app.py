#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家管理系统后端主应用
Author: Merchant Admin System
Date: 2024-12-12
"""

import os
from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_cors import CORS
from datetime import timedelta
import logging
from logging.handlers import RotatingFileHandler

# 创建Flask应用
app = Flask(__name__)

# 配置
class Config:
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'merchant-admin-secret-key-2024'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///merchant_admin.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-2024'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 其他配置
    JSON_AS_ASCII = False
    JSONIFY_PRETTYPRINT_REGULAR = True

app.config.from_object(Config)

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'images'), exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'documents'), exist_ok=True)

# 初始化扩展
db = SQLAlchemy(app)
migrate = Migrate(app, db)
jwt = JWTManager(app)
CORS(app, origins=['http://localhost:3000'], supports_credentials=True)

# 配置日志
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')
    file_handler = RotatingFileHandler('logs/merchant_admin.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('商家管理系统启动')

# 简化的模型导入
try:
    # 基础模型
    from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, JSON, Enum
    from datetime import datetime
    from enum import Enum as PyEnum

    # 创建基础模型
    class MerchantStatus(PyEnum):
        PENDING = 'pending'
        APPROVED = 'approved'
        REJECTED = 'rejected'
        SUSPENDED = 'suspended'

    class BusinessType(PyEnum):
        RESTAURANT = 'restaurant'
        FAST_FOOD = 'fast_food'
        CAFE = 'cafe'
        OTHER = 'other'

    class MerchantUserRole(PyEnum):
        OWNER = 'owner'
        MANAGER = 'manager'
        STAFF = 'staff'

    # 商家模型
    class Merchant(db.Model):
        __tablename__ = 'merchants'
        id = Column(Integer, primary_key=True)
        name = Column(String(100), nullable=False)
        business_name = Column(String(200), nullable=False)
        business_license = Column(String(50), unique=True, nullable=False)
        contact_person = Column(String(50), nullable=False)
        contact_phone = Column(String(20), nullable=False)
        email = Column(String(100), nullable=False)
        province = Column(String(50), nullable=False)
        city = Column(String(50), nullable=False)
        district = Column(String(50), nullable=False)
        address = Column(String(200), nullable=False)
        business_type = Column(String(50), nullable=False)
        description = Column(Text)
        status = Column(String(20), default='pending')
        is_active = Column(Boolean, default=False)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        approved_at = Column(DateTime)

    # 商家用户模型
    class MerchantUser(db.Model):
        __tablename__ = 'merchant_users'
        id = Column(Integer, primary_key=True)
        merchant_id = Column(Integer, db.ForeignKey('merchants.id'), nullable=False)
        username = Column(String(50), unique=True, nullable=False)
        email = Column(String(100), unique=True, nullable=False)
        phone = Column(String(20))
        password_hash = Column(String(255), nullable=False)
        role = Column(String(20), nullable=False)
        permissions = Column(JSON)
        is_active = Column(Boolean, default=True)
        last_login = Column(DateTime)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

        @property
        def merchant(self):
            return Merchant.query.get(self.merchant_id)

    app.logger.info('基础模型创建成功')

except Exception as e:
    app.logger.error(f'模型创建失败: {str(e)}')

# 简化的路由导入
try:
    from flask import Blueprint, request, jsonify
    from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity
    from werkzeug.security import check_password_hash, generate_password_hash

    # 创建认证蓝图
    auth_bp = Blueprint('auth', __name__)

    @auth_bp.route('/merchant/login', methods=['POST'])
    def merchant_login():
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')

            if not username or not password:
                return jsonify({
                    'success': False,
                    'message': '用户名和密码不能为空'
                }), 400

            # 查找用户
            user = MerchantUser.query.filter_by(username=username).first()

            if not user or not check_password_hash(user.password_hash, password):
                return jsonify({
                    'success': False,
                    'message': '用户名或密码错误'
                }), 401

            # 检查商家状态
            merchant = user.merchant
            if merchant.status != 'approved':
                return jsonify({
                    'success': False,
                    'message': '商家审核未通过，无法登录',
                    'data': {'status': merchant.status}
                }), 403

            # 生成token
            access_token = create_access_token(identity=user.id)
            refresh_token = create_refresh_token(identity=user.id)

            return jsonify({
                'success': True,
                'message': '登录成功',
                'data': {
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'role': user.role
                    },
                    'merchant': {
                        'id': merchant.id,
                        'name': merchant.name,
                        'status': merchant.status
                    },
                    'access_token': access_token,
                    'refresh_token': refresh_token
                }
            })

        except Exception as e:
            app.logger.error(f'登录失败: {str(e)}')
            return jsonify({
                'success': False,
                'message': '登录失败，请稍后重试'
            }), 500

    # 注册蓝图
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.logger.info('认证路由注册成功')

except Exception as e:
    app.logger.error(f'路由创建失败: {str(e)}')

# 注册蓝图（简化版本）
try:
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.logger.info('认证蓝图注册成功')
except Exception as e:
    app.logger.error(f'蓝图注册失败: {str(e)}')

# 全局错误处理
@app.errorhandler(400)
def bad_request(error):
    return jsonify({
        'success': False,
        'message': '请求参数错误',
        'error': str(error)
    }), 400

@app.errorhandler(401)
def unauthorized(error):
    return jsonify({
        'success': False,
        'message': '未授权访问',
        'error': str(error)
    }), 401

@app.errorhandler(403)
def forbidden(error):
    return jsonify({
        'success': False,
        'message': '禁止访问',
        'error': str(error)
    }), 403

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': '资源不存在',
        'error': str(error)
    }), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return jsonify({
        'success': False,
        'message': '服务器内部错误',
        'error': str(error)
    }), 500

# JWT错误处理
@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({
        'success': False,
        'message': 'Token已过期',
        'error': 'token_expired'
    }), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    return jsonify({
        'success': False,
        'message': 'Token无效',
        'error': 'token_invalid'
    }), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({
        'success': False,
        'message': '缺少访问令牌',
        'error': 'token_missing'
    }), 401

# 健康检查接口
@app.route('/api/health')
def health_check():
    return jsonify({
        'success': True,
        'message': '服务正常运行',
        'data': {
            'status': 'healthy',
            'version': '1.0.0',
            'timestamp': db.func.now()
        }
    })

# 根路径
@app.route('/')
def index():
    return jsonify({
        'success': True,
        'message': '商家管理系统API',
        'data': {
            'name': 'Merchant Admin System API',
            'version': '1.0.0',
            'description': '专业的餐饮商家管理平台后端API',
            'endpoints': {
                'auth': '/api/auth',
                'merchant': '/api/merchant',
                'products': '/api/products',
                'orders': '/api/orders',
                'refunds': '/api/refunds',
                'reviews': '/api/reviews',
                'delivery': '/api/delivery',
                'analytics': '/api/analytics',
                'upload': '/api/upload',
                'health': '/api/health'
            }
        }
    })

# 初始化数据库
def init_database():
    """初始化数据库"""
    try:
        with app.app_context():
            db.create_all()
            app.logger.info('数据库表创建成功')

            # 创建默认商家
            default_merchant = Merchant.query.filter_by(business_license='DEFAULT001').first()
            if not default_merchant:
                default_merchant = Merchant(
                    name='演示商家',
                    business_name='演示餐厅',
                    business_license='DEFAULT001',
                    contact_person='管理员',
                    contact_phone='***********',
                    email='<EMAIL>',
                    province='北京市',
                    city='北京市',
                    district='朝阳区',
                    address='演示地址123号',
                    business_type='restaurant',
                    description='这是一个演示商家账户',
                    status='approved',
                    is_active=True,
                    approved_at=datetime.utcnow()
                )
                db.session.add(default_merchant)
                db.session.commit()
                app.logger.info('默认商家创建成功')

            # 创建默认管理员
            default_user = MerchantUser.query.filter_by(username='admin').first()
            if not default_user:
                default_user = MerchantUser(
                    merchant_id=default_merchant.id,
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('123456'),
                    role='owner',
                    permissions={'all': True},
                    is_active=True
                )
                db.session.add(default_user)
                db.session.commit()
                app.logger.info('默认管理员创建成功: admin/123456')

            return True

    except Exception as e:
        app.logger.error(f'数据库初始化失败: {str(e)}')
        return False

if __name__ == '__main__':
    print("="*50)
    print("🚀 启动商家管理系统后端服务")
    print("="*50)
    print(f"📍 服务地址: http://localhost:5001")
    print(f"🔧 健康检查: http://localhost:5001/api/health")
    print(f"🔑 登录接口: http://localhost:5001/api/auth/merchant/login")
    print(f"👤 演示账户: admin / 123456")
    print("="*50)

    # 初始化数据库
    if init_database():
        print("✅ 数据库初始化成功")
    else:
        print("❌ 数据库初始化失败")

    print("🎉 服务启动中...")
    app.run(host='0.0.0.0', port=5001, debug=True)
