#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家管理系统应用包初始化
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager
from flask_cors import CORS

# 全局变量
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    from .config import config
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    CORS(app)
    
    # 注册蓝图
    from .routes import auth, merchant, product, order, refund, review, delivery, analytics, upload
    app.register_blueprint(auth.bp, url_prefix='/api/auth')
    app.register_blueprint(merchant.bp, url_prefix='/api/merchant')
    app.register_blueprint(product.bp, url_prefix='/api/products')
    app.register_blueprint(order.bp, url_prefix='/api/orders')
    app.register_blueprint(refund.bp, url_prefix='/api/refunds')
    app.register_blueprint(review.bp, url_prefix='/api/reviews')
    app.register_blueprint(delivery.bp, url_prefix='/api/delivery')
    app.register_blueprint(analytics.bp, url_prefix='/api/analytics')
    app.register_blueprint(upload.bp, url_prefix='/api/upload')
    
    return app
