#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型包初始化
导入所有模型以确保SQLAlchemy能够发现它们
"""

from .merchant import Merchant, MerchantUser, MerchantStatus, MerchantUserRole, BusinessType
from .product import Category, Product, ProductVariant, ProductStatus
from .order import Order, OrderItem, OrderStatus, PaymentMethod, DeliveryType
from .refund import OrderRefund, RefundItem, RefundStatus, RefundType, RefundReason
from .refund import CustomerService, ServiceMessage
from .delivery import DeliveryPerson, DeliveryArea, DeliveryTask, DeliveryTrack
from .delivery import DeliveryPersonStatus, DeliveryProvider
from .review import Review, ProductReview, ReviewType
from .notification import Notification, SystemMessage, NotificationType, NotificationPriority

# 导出所有模型
__all__ = [
    # 商家相关
    'Merchant', 'MerchantUser', 'MerchantStatus', 'MerchantUserRole', 'BusinessType',
    
    # 商品相关
    'Category', 'Product', 'ProductVariant', 'ProductStatus',
    
    # 订单相关
    'Order', 'OrderItem', 'OrderStatus', 'PaymentMethod', 'DeliveryType',
    
    # 退款相关
    'OrderRefund', 'RefundItem', 'RefundStatus', 'RefundType', 'RefundReason',
    'CustomerService', 'ServiceMessage',
    
    # 配送相关
    'DeliveryPerson', 'DeliveryArea', 'DeliveryTask', 'DeliveryTrack',
    'DeliveryPersonStatus', 'DeliveryProvider',
    
    # 评价相关
    'Review', 'ProductReview', 'ReviewType',
    
    # 通知相关
    'Notification', 'SystemMessage', 'NotificationType', 'NotificationPriority'
]
