from app import db
from datetime import datetime
import enum

class DeliveryPersonStatus(enum.Enum):
    """配送员状态枚举"""
    AVAILABLE = "available"      # 空闲
    BUSY = "busy"               # 忙碌
    OFFLINE = "offline"         # 离线
    SUSPENDED = "suspended"     # 暂停

class DeliveryProvider(enum.Enum):
    """配送服务商枚举"""
    SELF = "self"               # 自配送
    MEITUAN = "meituan"         # 美团配送
    ELEME = "eleme"             # 饿了么蜂鸟
    DADA = "dada"               # 达达配送
    SF = "sf"                   # 顺丰同城
    OTHER = "other"             # 其他

class DeliveryPerson(db.Model):
    """配送员模型"""
    __tablename__ = 'delivery_persons'
    
    id = db.Column(db.Integer, primary_key=True)
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    
    # 基本信息
    name = db.Column(db.String(50), nullable=False)  # 姓名
    phone = db.Column(db.String(20), nullable=False)  # 电话
    id_card = db.Column(db.String(20), nullable=False)  # 身份证号
    avatar = db.Column(db.String(255), nullable=True)  # 头像
    
    # 工作信息
    employee_id = db.Column(db.String(50), nullable=True)  # 工号
    vehicle_type = db.Column(db.String(20), nullable=False)  # 交通工具 (bike/motorcycle/car)
    vehicle_number = db.Column(db.String(20), nullable=True)  # 车牌号
    
    # 状态信息
    status = db.Column(db.Enum(DeliveryPersonStatus), default=DeliveryPersonStatus.OFFLINE)
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    
    # 位置信息
    current_latitude = db.Column(db.Float, nullable=True)  # 当前纬度
    current_longitude = db.Column(db.Float, nullable=True)  # 当前经度
    last_location_update = db.Column(db.DateTime, nullable=True)  # 最后位置更新时间
    
    # 统计信息
    total_orders = db.Column(db.Integer, default=0)  # 总订单数
    completed_orders = db.Column(db.Integer, default=0)  # 完成订单数
    rating = db.Column(db.Float, default=5.0)  # 平均评分
    rating_count = db.Column(db.Integer, default=0)  # 评价数量
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_online = db.Column(db.DateTime, nullable=True)  # 最后在线时间
    
    # 关联关系
    deliveries = db.relationship('DeliveryTask', backref='delivery_person', lazy=True)
    
    def __repr__(self):
        return f'<DeliveryPerson {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'merchant_id': self.merchant_id,
            'name': self.name,
            'phone': self.phone,
            'id_card': self.id_card,
            'avatar': self.avatar,
            'employee_id': self.employee_id,
            'vehicle_type': self.vehicle_type,
            'vehicle_number': self.vehicle_number,
            'status': self.status.value if self.status else None,
            'is_active': self.is_active,
            'current_latitude': self.current_latitude,
            'current_longitude': self.current_longitude,
            'last_location_update': self.last_location_update.isoformat() if self.last_location_update else None,
            'total_orders': self.total_orders,
            'completed_orders': self.completed_orders,
            'rating': self.rating,
            'rating_count': self.rating_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_online': self.last_online.isoformat() if self.last_online else None
        }

class DeliveryArea(db.Model):
    """配送区域模型"""
    __tablename__ = 'delivery_areas'
    
    id = db.Column(db.Integer, primary_key=True)
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    
    # 区域信息
    name = db.Column(db.String(100), nullable=False)  # 区域名称
    description = db.Column(db.Text, nullable=True)  # 区域描述
    
    # 地理信息
    polygon_coordinates = db.Column(db.JSON, nullable=False)  # 多边形坐标
    center_latitude = db.Column(db.Float, nullable=False)  # 中心纬度
    center_longitude = db.Column(db.Float, nullable=False)  # 中心经度
    
    # 配送设置
    delivery_fee = db.Column(db.Numeric(10, 2), nullable=False)  # 配送费
    min_order_amount = db.Column(db.Numeric(10, 2), default=0)  # 起送金额
    delivery_time = db.Column(db.Integer, default=30)  # 配送时间(分钟)
    
    # 状态
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    sort_order = db.Column(db.Integer, default=0)  # 排序
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<DeliveryArea {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'merchant_id': self.merchant_id,
            'name': self.name,
            'description': self.description,
            'polygon_coordinates': self.polygon_coordinates,
            'center_latitude': self.center_latitude,
            'center_longitude': self.center_longitude,
            'delivery_fee': float(self.delivery_fee) if self.delivery_fee else 0,
            'min_order_amount': float(self.min_order_amount) if self.min_order_amount else 0,
            'delivery_time': self.delivery_time,
            'is_active': self.is_active,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class DeliveryTask(db.Model):
    """配送任务模型"""
    __tablename__ = 'delivery_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, nullable=False)  # 订单ID
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    delivery_person_id = db.Column(db.Integer, db.ForeignKey('delivery_persons.id'), nullable=True)
    
    # 配送信息
    provider = db.Column(db.Enum(DeliveryProvider), default=DeliveryProvider.SELF)
    external_order_id = db.Column(db.String(100), nullable=True)  # 第三方订单ID
    
    # 地址信息
    pickup_address = db.Column(db.Text, nullable=False)  # 取餐地址
    pickup_latitude = db.Column(db.Float, nullable=False)  # 取餐纬度
    pickup_longitude = db.Column(db.Float, nullable=False)  # 取餐经度
    delivery_address = db.Column(db.Text, nullable=False)  # 配送地址
    delivery_latitude = db.Column(db.Float, nullable=False)  # 配送纬度
    delivery_longitude = db.Column(db.Float, nullable=False)  # 配送经度
    
    # 联系信息
    customer_name = db.Column(db.String(50), nullable=False)  # 客户姓名
    customer_phone = db.Column(db.String(20), nullable=False)  # 客户电话
    
    # 配送状态
    status = db.Column(db.String(20), default='pending')  # 状态
    estimated_pickup_time = db.Column(db.DateTime, nullable=True)  # 预计取餐时间
    estimated_delivery_time = db.Column(db.DateTime, nullable=True)  # 预计送达时间
    actual_pickup_time = db.Column(db.DateTime, nullable=True)  # 实际取餐时间
    actual_delivery_time = db.Column(db.DateTime, nullable=True)  # 实际送达时间
    
    # 费用信息
    delivery_fee = db.Column(db.Numeric(10, 2), nullable=False)  # 配送费
    distance = db.Column(db.Float, nullable=True)  # 配送距离(km)
    
    # 备注
    note = db.Column(db.Text, nullable=True)  # 配送备注
    cancel_reason = db.Column(db.Text, nullable=True)  # 取消原因
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    assigned_at = db.Column(db.DateTime, nullable=True)  # 分配时间
    completed_at = db.Column(db.DateTime, nullable=True)  # 完成时间
    
    # 关联关系
    tracks = db.relationship('DeliveryTrack', backref='task', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<DeliveryTask {self.id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'merchant_id': self.merchant_id,
            'delivery_person_id': self.delivery_person_id,
            'provider': self.provider.value if self.provider else None,
            'external_order_id': self.external_order_id,
            'pickup_address': self.pickup_address,
            'pickup_latitude': self.pickup_latitude,
            'pickup_longitude': self.pickup_longitude,
            'delivery_address': self.delivery_address,
            'delivery_latitude': self.delivery_latitude,
            'delivery_longitude': self.delivery_longitude,
            'customer_name': self.customer_name,
            'customer_phone': self.customer_phone,
            'status': self.status,
            'estimated_pickup_time': self.estimated_pickup_time.isoformat() if self.estimated_pickup_time else None,
            'estimated_delivery_time': self.estimated_delivery_time.isoformat() if self.estimated_delivery_time else None,
            'actual_pickup_time': self.actual_pickup_time.isoformat() if self.actual_pickup_time else None,
            'actual_delivery_time': self.actual_delivery_time.isoformat() if self.actual_delivery_time else None,
            'delivery_fee': float(self.delivery_fee) if self.delivery_fee else 0,
            'distance': self.distance,
            'note': self.note,
            'cancel_reason': self.cancel_reason,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'assigned_at': self.assigned_at.isoformat() if self.assigned_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'delivery_person_name': self.delivery_person.name if self.delivery_person else None
        }

class DeliveryTrack(db.Model):
    """配送轨迹模型"""
    __tablename__ = 'delivery_tracks'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('delivery_tasks.id'), nullable=False)
    
    # 位置信息
    latitude = db.Column(db.Float, nullable=False)  # 纬度
    longitude = db.Column(db.Float, nullable=False)  # 经度
    address = db.Column(db.String(255), nullable=True)  # 地址描述
    
    # 状态信息
    status = db.Column(db.String(50), nullable=False)  # 状态描述
    note = db.Column(db.Text, nullable=True)  # 备注
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<DeliveryTrack {self.id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'address': self.address,
            'status': self.status,
            'note': self.note,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
