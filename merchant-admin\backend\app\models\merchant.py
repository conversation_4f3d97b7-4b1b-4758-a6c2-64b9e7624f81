from app import db
from datetime import datetime
import enum

class MerchantStatus(enum.Enum):
    """商家状态枚举"""
    PENDING = "pending"      # 待审核
    APPROVED = "approved"    # 已通过
    REJECTED = "rejected"    # 已拒绝
    SUSPENDED = "suspended"  # 已暂停
    CLOSED = "closed"        # 已关闭

class BusinessType(enum.Enum):
    """商家类型枚举"""
    RESTAURANT = "restaurant"    # 餐厅
    FAST_FOOD = "fast_food"     # 快餐
    DESSERT = "dessert"         # 甜品
    BEVERAGE = "beverage"       # 饮品
    SNACK = "snack"            # 小食
    OTHER = "other"            # 其他

class Merchant(db.Model):
    """商家模型"""
    __tablename__ = 'merchants'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 基本信息
    name = db.Column(db.String(100), nullable=False)  # 商家名称
    business_name = db.Column(db.String(100), nullable=False)  # 营业执照名称
    business_license = db.Column(db.String(50), unique=True, nullable=False)  # 营业执照号
    contact_person = db.Column(db.String(50), nullable=False)  # 联系人
    contact_phone = db.Column(db.String(20), nullable=False)  # 联系电话
    email = db.Column(db.String(120), nullable=True)  # 邮箱
    
    # 地址信息
    province = db.Column(db.String(50), nullable=False)  # 省份
    city = db.Column(db.String(50), nullable=False)  # 城市
    district = db.Column(db.String(50), nullable=False)  # 区县
    address = db.Column(db.String(255), nullable=False)  # 详细地址
    latitude = db.Column(db.Float, nullable=True)  # 纬度
    longitude = db.Column(db.Float, nullable=True)  # 经度
    
    # 业务信息
    business_type = db.Column(db.Enum(BusinessType), nullable=False)  # 商家类型
    description = db.Column(db.Text, nullable=True)  # 商家描述
    logo = db.Column(db.String(255), nullable=True)  # 商家logo
    banner_images = db.Column(db.JSON, nullable=True)  # 轮播图
    
    # 营业信息
    opening_hours = db.Column(db.JSON, nullable=True)  # 营业时间
    delivery_fee = db.Column(db.Numeric(10, 2), default=0)  # 配送费
    min_order_amount = db.Column(db.Numeric(10, 2), default=0)  # 起送金额
    delivery_radius = db.Column(db.Float, default=5.0)  # 配送半径(km)
    
    # 状态信息
    status = db.Column(db.Enum(MerchantStatus), default=MerchantStatus.PENDING)
    is_active = db.Column(db.Boolean, default=True)  # 是否营业中
    is_featured = db.Column(db.Boolean, default=False)  # 是否推荐商家
    
    # 评分信息
    rating = db.Column(db.Float, default=5.0)  # 平均评分
    rating_count = db.Column(db.Integer, default=0)  # 评价数量
    
    # 财务信息
    commission_rate = db.Column(db.Float, default=0.1)  # 佣金比例
    balance = db.Column(db.Numeric(10, 2), default=0)  # 账户余额
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    approved_at = db.Column(db.DateTime, nullable=True)  # 审核通过时间
    
    # 关联关系
    users = db.relationship('MerchantUser', backref='merchant', lazy=True, cascade='all, delete-orphan')
    categories = db.relationship('Category', backref='merchant', lazy=True, cascade='all, delete-orphan')
    products = db.relationship('Product', backref='merchant', lazy=True, cascade='all, delete-orphan')
    orders = db.relationship('Order', backref='merchant', lazy=True)
    
    def __repr__(self):
        return f'<Merchant {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'business_name': self.business_name,
            'business_license': self.business_license,
            'contact_person': self.contact_person,
            'contact_phone': self.contact_phone,
            'email': self.email,
            'province': self.province,
            'city': self.city,
            'district': self.district,
            'address': self.address,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'business_type': self.business_type.value if self.business_type else None,
            'description': self.description,
            'logo': self.logo,
            'banner_images': self.banner_images,
            'opening_hours': self.opening_hours,
            'delivery_fee': float(self.delivery_fee) if self.delivery_fee else 0,
            'min_order_amount': float(self.min_order_amount) if self.min_order_amount else 0,
            'delivery_radius': self.delivery_radius,
            'status': self.status.value if self.status else None,
            'is_active': self.is_active,
            'is_featured': self.is_featured,
            'rating': self.rating,
            'rating_count': self.rating_count,
            'commission_rate': self.commission_rate,
            'balance': float(self.balance) if self.balance else 0,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None
        }

class MerchantUserRole(enum.Enum):
    """商家用户角色枚举"""
    OWNER = "owner"          # 店主
    MANAGER = "manager"      # 经理
    STAFF = "staff"         # 员工
    CASHIER = "cashier"     # 收银员

class MerchantUser(db.Model):
    """商家用户模型"""
    __tablename__ = 'merchant_users'
    
    id = db.Column(db.Integer, primary_key=True)
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    
    # 用户信息
    username = db.Column(db.String(80), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # 角色权限
    role = db.Column(db.Enum(MerchantUserRole), nullable=False)
    permissions = db.Column(db.JSON, nullable=True)  # 具体权限列表
    
    # 状态信息
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 唯一约束
    __table_args__ = (
        db.UniqueConstraint('merchant_id', 'username', name='uq_merchant_username'),
        db.UniqueConstraint('merchant_id', 'email', name='uq_merchant_email'),
    )
    
    def __repr__(self):
        return f'<MerchantUser {self.username}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'merchant_id': self.merchant_id,
            'username': self.username,
            'email': self.email,
            'phone': self.phone,
            'role': self.role.value if self.role else None,
            'permissions': self.permissions,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
