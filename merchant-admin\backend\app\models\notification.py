#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知相关数据模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class NotificationType(Enum):
    """通知类型枚举"""
    ORDER = 'order'            # 订单通知
    PAYMENT = 'payment'        # 支付通知
    REFUND = 'refund'          # 退款通知
    REVIEW = 'review'          # 评价通知
    SYSTEM = 'system'          # 系统通知
    PROMOTION = 'promotion'    # 促销通知

class NotificationPriority(Enum):
    """通知优先级枚举"""
    LOW = 'low'                # 低
    NORMAL = 'normal'          # 普通
    HIGH = 'high'              # 高
    URGENT = 'urgent'          # 紧急

class Notification(db.Model):
    """通知模型"""
    __tablename__ = 'notifications'
    
    id = Column(Integer, primary_key=True)
    merchant_id = Column(Integer, db.<PERSON><PERSON><PERSON>('merchants.id'), nullable=False, comment='商家ID')
    user_id = Column(Integer, db.ForeignKey('merchant_users.id'), comment='用户ID')
    
    # 通知信息
    type = Column(db.Enum(NotificationType), nullable=False, comment='通知类型')
    priority = Column(db.Enum(NotificationPriority), default=NotificationPriority.NORMAL, comment='优先级')
    title = Column(String(200), nullable=False, comment='标题')
    content = Column(Text, nullable=False, comment='内容')
    data = Column(JSON, comment='附加数据')
    
    # 状态信息
    is_read = Column(Boolean, default=False, comment='是否已读')
    is_sent = Column(Boolean, default=False, comment='是否已发送')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    read_at = Column(DateTime, comment='阅读时间')
    sent_at = Column(DateTime, comment='发送时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'merchant_id': self.merchant_id,
            'user_id': self.user_id,
            'type': self.type.value if self.type else None,
            'priority': self.priority.value if self.priority else None,
            'title': self.title,
            'content': self.content,
            'data': self.data,
            'is_read': self.is_read,
            'is_sent': self.is_sent,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None
        }

class SystemMessage(db.Model):
    """系统消息模型"""
    __tablename__ = 'system_messages'
    
    id = Column(Integer, primary_key=True)
    
    # 消息信息
    title = Column(String(200), nullable=False, comment='标题')
    content = Column(Text, nullable=False, comment='内容')
    type = Column(String(50), default='info', comment='消息类型')
    
    # 发布范围
    target_merchants = Column(JSON, comment='目标商家ID列表')
    target_users = Column(JSON, comment='目标用户ID列表')
    
    # 状态信息
    is_published = Column(Boolean, default=False, comment='是否发布')
    is_pinned = Column(Boolean, default=False, comment='是否置顶')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    published_at = Column(DateTime, comment='发布时间')
    expires_at = Column(DateTime, comment='过期时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'type': self.type,
            'target_merchants': self.target_merchants,
            'target_users': self.target_users,
            'is_published': self.is_published,
            'is_pinned': self.is_pinned,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'published_at': self.published_at.isoformat() if self.published_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }
