from app import db
from datetime import datetime
import enum
import uuid

class OrderStatus(enum.Enum):
    """订单状态枚举"""
    PENDING_PAYMENT = "pending_payment"      # 待支付
    PENDING_CONFIRM = "pending_confirm"      # 待确认
    CONFIRMED = "confirmed"                  # 已确认
    PREPARING = "preparing"                  # 制作中
    READY = "ready"                         # 待取餐
    DELIVERING = "delivering"               # 配送中
    DELIVERED = "delivered"                 # 已送达
    COMPLETED = "completed"                 # 已完成
    CANCELLED = "cancelled"                 # 已取消
    REFUNDING = "refunding"                 # 退款中
    REFUNDED = "refunded"                   # 已退款

class PaymentMethod(enum.Enum):
    """支付方式枚举"""
    WECHAT = "wechat"           # 微信支付
    ALIPAY = "alipay"          # 支付宝
    CASH = "cash"              # 现金
    CARD = "card"              # 银行卡
    BALANCE = "balance"        # 余额支付

class DeliveryType(enum.Enum):
    """配送方式枚举"""
    DELIVERY = "delivery"       # 外卖配送
    PICKUP = "pickup"          # 到店自取
    DINE_IN = "dine_in"        # 堂食

class Order(db.Model):
    """订单模型"""
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False)  # 订单号
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    user_id = db.Column(db.Integer, nullable=False)  # 用户ID (来自用户系统)
    
    # 订单状态
    status = db.Column(db.Enum(OrderStatus), default=OrderStatus.PENDING_PAYMENT)
    
    # 配送信息
    delivery_type = db.Column(db.Enum(DeliveryType), nullable=False)
    delivery_address = db.Column(db.Text, nullable=True)  # 配送地址
    delivery_phone = db.Column(db.String(20), nullable=True)  # 配送电话
    delivery_name = db.Column(db.String(50), nullable=True)  # 收货人姓名
    delivery_latitude = db.Column(db.Float, nullable=True)  # 配送地址纬度
    delivery_longitude = db.Column(db.Float, nullable=True)  # 配送地址经度
    delivery_distance = db.Column(db.Float, nullable=True)  # 配送距离(km)
    
    # 配送员信息
    delivery_person_id = db.Column(db.Integer, nullable=True)  # 配送员ID
    delivery_person_name = db.Column(db.String(50), nullable=True)  # 配送员姓名
    delivery_person_phone = db.Column(db.String(20), nullable=True)  # 配送员电话
    
    # 金额信息
    subtotal = db.Column(db.Numeric(10, 2), nullable=False)  # 商品小计
    delivery_fee = db.Column(db.Numeric(10, 2), default=0)  # 配送费
    discount_amount = db.Column(db.Numeric(10, 2), default=0)  # 优惠金额
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)  # 总金额
    paid_amount = db.Column(db.Numeric(10, 2), default=0)  # 已支付金额
    
    # 支付信息
    payment_method = db.Column(db.Enum(PaymentMethod), nullable=True)
    payment_status = db.Column(db.String(20), default='unpaid')  # 支付状态
    payment_time = db.Column(db.DateTime, nullable=True)  # 支付时间
    payment_transaction_id = db.Column(db.String(100), nullable=True)  # 支付交易号
    
    # 时间信息
    estimated_delivery_time = db.Column(db.DateTime, nullable=True)  # 预计送达时间
    actual_delivery_time = db.Column(db.DateTime, nullable=True)  # 实际送达时间
    preparation_time = db.Column(db.Integer, default=30)  # 制作时间(分钟)
    
    # 备注信息
    customer_note = db.Column(db.Text, nullable=True)  # 客户备注
    merchant_note = db.Column(db.Text, nullable=True)  # 商家备注
    cancel_reason = db.Column(db.Text, nullable=True)  # 取消原因
    
    # 评价信息
    is_rated = db.Column(db.Boolean, default=False)  # 是否已评价
    rating = db.Column(db.Float, nullable=True)  # 评分
    review = db.Column(db.Text, nullable=True)  # 评价内容
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    confirmed_at = db.Column(db.DateTime, nullable=True)  # 确认时间
    completed_at = db.Column(db.DateTime, nullable=True)  # 完成时间
    
    # 关联关系
    items = db.relationship('OrderItem', backref='order', lazy=True, cascade='all, delete-orphan')
    refunds = db.relationship('OrderRefund', backref='order', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(Order, self).__init__(**kwargs)
        if not self.order_number:
            self.order_number = self.generate_order_number()
    
    @staticmethod
    def generate_order_number():
        """生成订单号"""
        import time
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4().hex)[:6].upper()
        return f"ORD{timestamp}{random_str}"
    
    def __repr__(self):
        return f'<Order {self.order_number}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_number': self.order_number,
            'merchant_id': self.merchant_id,
            'user_id': self.user_id,
            'status': self.status.value if self.status else None,
            'delivery_type': self.delivery_type.value if self.delivery_type else None,
            'delivery_address': self.delivery_address,
            'delivery_phone': self.delivery_phone,
            'delivery_name': self.delivery_name,
            'delivery_latitude': self.delivery_latitude,
            'delivery_longitude': self.delivery_longitude,
            'delivery_distance': self.delivery_distance,
            'delivery_person_id': self.delivery_person_id,
            'delivery_person_name': self.delivery_person_name,
            'delivery_person_phone': self.delivery_person_phone,
            'subtotal': float(self.subtotal) if self.subtotal else 0,
            'delivery_fee': float(self.delivery_fee) if self.delivery_fee else 0,
            'discount_amount': float(self.discount_amount) if self.discount_amount else 0,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'paid_amount': float(self.paid_amount) if self.paid_amount else 0,
            'payment_method': self.payment_method.value if self.payment_method else None,
            'payment_status': self.payment_status,
            'payment_time': self.payment_time.isoformat() if self.payment_time else None,
            'payment_transaction_id': self.payment_transaction_id,
            'estimated_delivery_time': self.estimated_delivery_time.isoformat() if self.estimated_delivery_time else None,
            'actual_delivery_time': self.actual_delivery_time.isoformat() if self.actual_delivery_time else None,
            'preparation_time': self.preparation_time,
            'customer_note': self.customer_note,
            'merchant_note': self.merchant_note,
            'cancel_reason': self.cancel_reason,
            'is_rated': self.is_rated,
            'rating': self.rating,
            'review': self.review,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'confirmed_at': self.confirmed_at.isoformat() if self.confirmed_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'items': [item.to_dict() for item in self.items] if self.items else [],
            'merchant_name': self.merchant.name if self.merchant else None
        }

class OrderItem(db.Model):
    """订单项模型"""
    __tablename__ = 'order_items'
    
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    product_id = db.Column(db.Integer, nullable=False)  # 商品ID
    variant_id = db.Column(db.Integer, nullable=True)  # 规格ID
    
    # 商品信息快照
    product_name = db.Column(db.String(200), nullable=False)  # 商品名称
    product_image = db.Column(db.String(255), nullable=True)  # 商品图片
    variant_name = db.Column(db.String(100), nullable=True)  # 规格名称
    
    # 价格和数量
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)  # 单价
    quantity = db.Column(db.Integer, nullable=False)  # 数量
    subtotal = db.Column(db.Numeric(10, 2), nullable=False)  # 小计
    
    # 备注
    note = db.Column(db.Text, nullable=True)  # 备注
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<OrderItem {self.product_name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'product_id': self.product_id,
            'variant_id': self.variant_id,
            'product_name': self.product_name,
            'product_image': self.product_image,
            'variant_name': self.variant_name,
            'unit_price': float(self.unit_price) if self.unit_price else 0,
            'quantity': self.quantity,
            'subtotal': float(self.subtotal) if self.subtotal else 0,
            'note': self.note,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
