from app import db
from datetime import datetime
import enum

class RefundStatus(enum.Enum):
    """退款状态枚举"""
    PENDING = "pending"          # 待处理
    APPROVED = "approved"        # 已同意
    REJECTED = "rejected"        # 已拒绝
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败

class RefundType(enum.Enum):
    """退款类型枚举"""
    FULL_REFUND = "full_refund"      # 全额退款
    PARTIAL_REFUND = "partial_refund" # 部分退款
    ITEM_REFUND = "item_refund"      # 单品退款

class RefundReason(enum.Enum):
    """退款原因枚举"""
    QUALITY_ISSUE = "quality_issue"           # 质量问题
    WRONG_ORDER = "wrong_order"               # 订单错误
    DELIVERY_DELAY = "delivery_delay"         # 配送延迟
    CUSTOMER_CHANGE = "customer_change"       # 客户改变主意
    MERCHANT_CANCEL = "merchant_cancel"       # 商家取消
    SYSTEM_ERROR = "system_error"             # 系统错误
    OTHER = "other"                          # 其他原因

class OrderRefund(db.Model):
    """订单退款模型"""
    __tablename__ = 'order_refunds'
    
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    refund_number = db.Column(db.String(50), unique=True, nullable=False)  # 退款单号
    
    # 退款信息
    refund_type = db.Column(db.Enum(RefundType), nullable=False)
    refund_reason = db.Column(db.Enum(RefundReason), nullable=False)
    refund_amount = db.Column(db.Numeric(10, 2), nullable=False)  # 退款金额
    
    # 申请信息
    applicant_id = db.Column(db.Integer, nullable=False)  # 申请人ID
    applicant_type = db.Column(db.String(20), nullable=False)  # 申请人类型 (customer/merchant)
    application_reason = db.Column(db.Text, nullable=False)  # 申请理由
    evidence_images = db.Column(db.JSON, nullable=True)  # 证据图片
    
    # 处理信息
    status = db.Column(db.Enum(RefundStatus), default=RefundStatus.PENDING)
    handler_id = db.Column(db.Integer, nullable=True)  # 处理人ID
    handler_note = db.Column(db.Text, nullable=True)  # 处理备注
    reject_reason = db.Column(db.Text, nullable=True)  # 拒绝原因
    
    # 退款处理
    refund_method = db.Column(db.String(20), nullable=True)  # 退款方式
    refund_account = db.Column(db.String(100), nullable=True)  # 退款账户
    refund_transaction_id = db.Column(db.String(100), nullable=True)  # 退款交易号
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    processed_at = db.Column(db.DateTime, nullable=True)  # 处理时间
    completed_at = db.Column(db.DateTime, nullable=True)  # 完成时间
    
    # 关联关系
    items = db.relationship('RefundItem', backref='refund', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(OrderRefund, self).__init__(**kwargs)
        if not self.refund_number:
            self.refund_number = self.generate_refund_number()
    
    @staticmethod
    def generate_refund_number():
        """生成退款单号"""
        import time
        import uuid
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4().hex)[:6].upper()
        return f"REF{timestamp}{random_str}"
    
    def __repr__(self):
        return f'<OrderRefund {self.refund_number}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'refund_number': self.refund_number,
            'refund_type': self.refund_type.value if self.refund_type else None,
            'refund_reason': self.refund_reason.value if self.refund_reason else None,
            'refund_amount': float(self.refund_amount) if self.refund_amount else 0,
            'applicant_id': self.applicant_id,
            'applicant_type': self.applicant_type,
            'application_reason': self.application_reason,
            'evidence_images': self.evidence_images,
            'status': self.status.value if self.status else None,
            'handler_id': self.handler_id,
            'handler_note': self.handler_note,
            'reject_reason': self.reject_reason,
            'refund_method': self.refund_method,
            'refund_account': self.refund_account,
            'refund_transaction_id': self.refund_transaction_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'items': [item.to_dict() for item in self.items] if self.items else [],
            'order_number': self.order.order_number if self.order else None
        }

class RefundItem(db.Model):
    """退款项目模型"""
    __tablename__ = 'refund_items'
    
    id = db.Column(db.Integer, primary_key=True)
    refund_id = db.Column(db.Integer, db.ForeignKey('order_refunds.id'), nullable=False)
    order_item_id = db.Column(db.Integer, nullable=False)  # 订单项ID
    
    # 商品信息
    product_name = db.Column(db.String(200), nullable=False)  # 商品名称
    variant_name = db.Column(db.String(100), nullable=True)  # 规格名称
    
    # 退款信息
    refund_quantity = db.Column(db.Integer, nullable=False)  # 退款数量
    refund_amount = db.Column(db.Numeric(10, 2), nullable=False)  # 退款金额
    reason = db.Column(db.Text, nullable=True)  # 退款原因
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<RefundItem {self.product_name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'refund_id': self.refund_id,
            'order_item_id': self.order_item_id,
            'product_name': self.product_name,
            'variant_name': self.variant_name,
            'refund_quantity': self.refund_quantity,
            'refund_amount': float(self.refund_amount) if self.refund_amount else 0,
            'reason': self.reason,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class CustomerService(db.Model):
    """客服工单模型"""
    __tablename__ = 'customer_services'
    
    id = db.Column(db.Integer, primary_key=True)
    ticket_number = db.Column(db.String(50), unique=True, nullable=False)  # 工单号
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    
    # 工单信息
    customer_id = db.Column(db.Integer, nullable=False)  # 客户ID
    order_id = db.Column(db.Integer, nullable=True)  # 关联订单ID
    category = db.Column(db.String(50), nullable=False)  # 问题分类
    priority = db.Column(db.String(20), default='normal')  # 优先级 (low/normal/high/urgent)
    
    # 内容信息
    title = db.Column(db.String(200), nullable=False)  # 标题
    description = db.Column(db.Text, nullable=False)  # 问题描述
    attachments = db.Column(db.JSON, nullable=True)  # 附件
    
    # 状态信息
    status = db.Column(db.String(20), default='open')  # 状态 (open/in_progress/resolved/closed)
    assigned_to = db.Column(db.Integer, nullable=True)  # 分配给谁
    
    # 解决信息
    resolution = db.Column(db.Text, nullable=True)  # 解决方案
    satisfaction_rating = db.Column(db.Integer, nullable=True)  # 满意度评分 (1-5)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    resolved_at = db.Column(db.DateTime, nullable=True)  # 解决时间
    closed_at = db.Column(db.DateTime, nullable=True)  # 关闭时间
    
    # 关联关系
    messages = db.relationship('ServiceMessage', backref='ticket', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(CustomerService, self).__init__(**kwargs)
        if not self.ticket_number:
            self.ticket_number = self.generate_ticket_number()
    
    @staticmethod
    def generate_ticket_number():
        """生成工单号"""
        import time
        import uuid
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4().hex)[:6].upper()
        return f"CS{timestamp}{random_str}"
    
    def __repr__(self):
        return f'<CustomerService {self.ticket_number}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'ticket_number': self.ticket_number,
            'merchant_id': self.merchant_id,
            'customer_id': self.customer_id,
            'order_id': self.order_id,
            'category': self.category,
            'priority': self.priority,
            'title': self.title,
            'description': self.description,
            'attachments': self.attachments,
            'status': self.status,
            'assigned_to': self.assigned_to,
            'resolution': self.resolution,
            'satisfaction_rating': self.satisfaction_rating,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None,
            'closed_at': self.closed_at.isoformat() if self.closed_at else None,
            'messages_count': len(self.messages) if self.messages else 0
        }

class ServiceMessage(db.Model):
    """客服消息模型"""
    __tablename__ = 'service_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    ticket_id = db.Column(db.Integer, db.ForeignKey('customer_services.id'), nullable=False)
    
    # 消息信息
    sender_id = db.Column(db.Integer, nullable=False)  # 发送者ID
    sender_type = db.Column(db.String(20), nullable=False)  # 发送者类型 (customer/merchant/system)
    message_type = db.Column(db.String(20), default='text')  # 消息类型 (text/image/file)
    content = db.Column(db.Text, nullable=False)  # 消息内容
    attachments = db.Column(db.JSON, nullable=True)  # 附件
    
    # 状态信息
    is_read = db.Column(db.Boolean, default=False)  # 是否已读
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<ServiceMessage {self.id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'ticket_id': self.ticket_id,
            'sender_id': self.sender_id,
            'sender_type': self.sender_type,
            'message_type': self.message_type,
            'content': self.content,
            'attachments': self.attachments,
            'is_read': self.is_read,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
