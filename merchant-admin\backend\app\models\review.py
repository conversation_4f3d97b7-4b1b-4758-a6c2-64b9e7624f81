from app import db
from datetime import datetime
import enum

class ReviewType(enum.Enum):
    """评价类型枚举"""
    ORDER = "order"          # 订单评价
    PRODUCT = "product"      # 商品评价
    DELIVERY = "delivery"    # 配送评价
    SERVICE = "service"      # 服务评价

class Review(db.Model):
    """评价模型"""
    __tablename__ = 'reviews'
    
    id = db.Column(db.Integer, primary_key=True)
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    order_id = db.Column(db.Integer, nullable=False)  # 订单ID
    customer_id = db.Column(db.Integer, nullable=False)  # 客户ID
    
    # 评价信息
    review_type = db.Column(db.Enum(ReviewType), default=ReviewType.ORDER)
    rating = db.Column(db.Integer, nullable=False)  # 评分 (1-5)
    content = db.Column(db.Text, nullable=True)  # 评价内容
    images = db.Column(db.JSO<PERSON>, nullable=True)  # 评价图片
    
    # 详细评分
    food_rating = db.Column(db.Integer, nullable=True)  # 菜品评分
    service_rating = db.Column(db.Integer, nullable=True)  # 服务评分
    delivery_rating = db.Column(db.Integer, nullable=True)  # 配送评分
    
    # 标签
    tags = db.Column(db.JSON, nullable=True)  # 评价标签
    
    # 商家回复
    merchant_reply = db.Column(db.Text, nullable=True)  # 商家回复
    reply_time = db.Column(db.DateTime, nullable=True)  # 回复时间
    replied_by = db.Column(db.Integer, nullable=True)  # 回复人ID
    
    # 状态信息
    is_anonymous = db.Column(db.Boolean, default=False)  # 是否匿名
    is_visible = db.Column(db.Boolean, default=True)  # 是否显示
    is_featured = db.Column(db.Boolean, default=False)  # 是否精选
    
    # 统计信息
    helpful_count = db.Column(db.Integer, default=0)  # 有用数
    report_count = db.Column(db.Integer, default=0)  # 举报数
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Review {self.id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'merchant_id': self.merchant_id,
            'order_id': self.order_id,
            'customer_id': self.customer_id,
            'review_type': self.review_type.value if self.review_type else None,
            'rating': self.rating,
            'content': self.content,
            'images': self.images,
            'food_rating': self.food_rating,
            'service_rating': self.service_rating,
            'delivery_rating': self.delivery_rating,
            'tags': self.tags,
            'merchant_reply': self.merchant_reply,
            'reply_time': self.reply_time.isoformat() if self.reply_time else None,
            'replied_by': self.replied_by,
            'is_anonymous': self.is_anonymous,
            'is_visible': self.is_visible,
            'is_featured': self.is_featured,
            'helpful_count': self.helpful_count,
            'report_count': self.report_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ProductReview(db.Model):
    """商品评价模型"""
    __tablename__ = 'product_reviews'
    
    id = db.Column(db.Integer, primary_key=True)
    review_id = db.Column(db.Integer, db.ForeignKey('reviews.id'), nullable=False)
    product_id = db.Column(db.Integer, nullable=False)  # 商品ID
    
    # 商品评价信息
    product_rating = db.Column(db.Integer, nullable=False)  # 商品评分
    taste_rating = db.Column(db.Integer, nullable=True)  # 口味评分
    portion_rating = db.Column(db.Integer, nullable=True)  # 分量评分
    packaging_rating = db.Column(db.Integer, nullable=True)  # 包装评分
    
    # 商品信息快照
    product_name = db.Column(db.String(200), nullable=False)  # 商品名称
    variant_name = db.Column(db.String(100), nullable=True)  # 规格名称
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<ProductReview {self.id}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'review_id': self.review_id,
            'product_id': self.product_id,
            'product_rating': self.product_rating,
            'taste_rating': self.taste_rating,
            'portion_rating': self.portion_rating,
            'packaging_rating': self.packaging_rating,
            'product_name': self.product_name,
            'variant_name': self.variant_name,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class NotificationType(enum.Enum):
    """通知类型枚举"""
    ORDER = "order"              # 订单通知
    PAYMENT = "payment"          # 支付通知
    REFUND = "refund"           # 退款通知
    REVIEW = "review"           # 评价通知
    SYSTEM = "system"           # 系统通知
    PROMOTION = "promotion"     # 促销通知
    DELIVERY = "delivery"       # 配送通知

class NotificationPriority(enum.Enum):
    """通知优先级枚举"""
    LOW = "low"                 # 低
    NORMAL = "normal"           # 普通
    HIGH = "high"               # 高
    URGENT = "urgent"           # 紧急

class Notification(db.Model):
    """通知模型"""
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    
    # 通知信息
    type = db.Column(db.Enum(NotificationType), nullable=False)
    priority = db.Column(db.Enum(NotificationPriority), default=NotificationPriority.NORMAL)
    title = db.Column(db.String(200), nullable=False)  # 标题
    content = db.Column(db.Text, nullable=False)  # 内容
    
    # 关联信息
    related_id = db.Column(db.Integer, nullable=True)  # 关联对象ID
    related_type = db.Column(db.String(50), nullable=True)  # 关联对象类型
    
    # 跳转信息
    action_url = db.Column(db.String(255), nullable=True)  # 跳转链接
    action_text = db.Column(db.String(50), nullable=True)  # 操作文本
    
    # 状态信息
    is_read = db.Column(db.Boolean, default=False)  # 是否已读
    is_sent = db.Column(db.Boolean, default=False)  # 是否已发送
    
    # 发送渠道
    send_email = db.Column(db.Boolean, default=False)  # 是否发送邮件
    send_sms = db.Column(db.Boolean, default=False)  # 是否发送短信
    send_push = db.Column(db.Boolean, default=True)  # 是否推送
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime, nullable=True)  # 阅读时间
    sent_at = db.Column(db.DateTime, nullable=True)  # 发送时间
    
    def __repr__(self):
        return f'<Notification {self.title}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'merchant_id': self.merchant_id,
            'type': self.type.value if self.type else None,
            'priority': self.priority.value if self.priority else None,
            'title': self.title,
            'content': self.content,
            'related_id': self.related_id,
            'related_type': self.related_type,
            'action_url': self.action_url,
            'action_text': self.action_text,
            'is_read': self.is_read,
            'is_sent': self.is_sent,
            'send_email': self.send_email,
            'send_sms': self.send_sms,
            'send_push': self.send_push,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None
        }

class SystemMessage(db.Model):
    """系统消息模型"""
    __tablename__ = 'system_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 消息信息
    title = db.Column(db.String(200), nullable=False)  # 标题
    content = db.Column(db.Text, nullable=False)  # 内容
    message_type = db.Column(db.String(50), default='info')  # 消息类型
    
    # 目标用户
    target_type = db.Column(db.String(20), default='all')  # 目标类型 (all/merchant/specific)
    target_merchants = db.Column(db.JSON, nullable=True)  # 目标商家列表
    
    # 显示设置
    is_popup = db.Column(db.Boolean, default=False)  # 是否弹窗
    is_sticky = db.Column(db.Boolean, default=False)  # 是否置顶
    
    # 状态信息
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    
    # 时间设置
    start_time = db.Column(db.DateTime, nullable=True)  # 开始时间
    end_time = db.Column(db.DateTime, nullable=True)  # 结束时间
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<SystemMessage {self.title}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'message_type': self.message_type,
            'target_type': self.target_type,
            'target_merchants': self.target_merchants,
            'is_popup': self.is_popup,
            'is_sticky': self.is_sticky,
            'is_active': self.is_active,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
