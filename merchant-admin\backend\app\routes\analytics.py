#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据统计相关路由
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta

bp = Blueprint('analytics', __name__)

@bp.route('/sales', methods=['GET'])
@jwt_required()
def get_sales_analytics():
    """获取销售统计"""
    try:
        # 模拟销售数据
        sales_data = {
            'today': {
                'orders': 156,
                'revenue': 8650.50,
                'avg_order_value': 55.45
            },
            'week': {
                'orders': 890,
                'revenue': 45230.80,
                'avg_order_value': 50.82
            },
            'month': {
                'orders': 3420,
                'revenue': 125680.00,
                'avg_order_value': 36.75
            },
            'trend': [
                {'date': '2024-12-06', 'orders': 120, 'revenue': 6200},
                {'date': '2024-12-07', 'orders': 132, 'revenue': 7100},
                {'date': '2024-12-08', 'orders': 101, 'revenue': 5800},
                {'date': '2024-12-09', 'orders': 134, 'revenue': 7300},
                {'date': '2024-12-10', 'orders': 90, 'revenue': 5200},
                {'date': '2024-12-11', 'orders': 230, 'revenue': 12500},
                {'date': '2024-12-12', 'orders': 156, 'revenue': 8650}
            ]
        }
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': sales_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取销售统计失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取销售统计失败'
        }), 500

@bp.route('/products', methods=['GET'])
@jwt_required()
def get_product_analytics():
    """获取商品分析"""
    try:
        # 模拟商品分析数据
        product_data = {
            'top_selling': [
                {'name': '宫保鸡丁', 'sales': 89, 'revenue': 2670},
                {'name': '麻婆豆腐', 'sales': 76, 'revenue': 2280},
                {'name': '红烧肉', 'sales': 65, 'revenue': 2600}
            ],
            'low_stock': [
                {'name': '宫保鸡丁', 'stock': 5, 'min_stock': 10},
                {'name': '麻婆豆腐', 'stock': 3, 'min_stock': 8}
            ]
        }
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': product_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取商品分析失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取商品分析失败'
        }), 500
