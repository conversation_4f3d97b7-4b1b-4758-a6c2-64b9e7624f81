#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证相关路由
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import (
    create_access_token, create_refresh_token, jwt_required, 
    get_jwt_identity, get_jwt
)
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime, timedelta
import re
import random
import string

from app import db
from app.models.merchant import Merchant, MerchantUser, MerchantStatus, MerchantUserRole, BusinessType
from app.utils.validators import validate_email, validate_phone, validate_password
from app.utils.helpers import send_verification_code, send_email

bp = Blueprint('auth', __name__)

# 存储验证码的临时字典（生产环境应使用Redis）
verification_codes = {}

@bp.route('/merchant/register', methods=['POST'])
def merchant_register():
    """商家注册"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = [
            'merchant_name', 'business_name', 'business_license',
            'contact_person', 'contact_phone', 'email', 'username', 
            'password', 'address', 'business_type'
        ]
        
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 验证邮箱格式
        if not validate_email(data['email']):
            return jsonify({
                'success': False,
                'message': '邮箱格式不正确'
            }), 400
        
        # 验证手机号格式
        if not validate_phone(data['contact_phone']):
            return jsonify({
                'success': False,
                'message': '手机号格式不正确'
            }), 400
        
        # 验证密码强度
        if not validate_password(data['password']):
            return jsonify({
                'success': False,
                'message': '密码必须包含字母和数字，长度6-20位'
            }), 400
        
        # 检查营业执照号是否已存在
        existing_merchant = Merchant.query.filter_by(
            business_license=data['business_license']
        ).first()
        if existing_merchant:
            return jsonify({
                'success': False,
                'message': '该营业执照号已被注册'
            }), 400
        
        # 检查用户名是否已存在
        existing_user = MerchantUser.query.filter_by(
            username=data['username']
        ).first()
        if existing_user:
            return jsonify({
                'success': False,
                'message': '用户名已存在'
            }), 400
        
        # 检查邮箱是否已存在
        existing_email = MerchantUser.query.filter_by(
            email=data['email']
        ).first()
        if existing_email:
            return jsonify({
                'success': False,
                'message': '邮箱已被注册'
            }), 400
        
        # 创建商家记录
        merchant = Merchant(
            name=data['merchant_name'],
            business_name=data['business_name'],
            business_license=data['business_license'],
            contact_person=data['contact_person'],
            contact_phone=data['contact_phone'],
            email=data['email'],
            province=data['address']['province'],
            city=data['address']['city'],
            district=data['address']['district'],
            address=data['address']['detail'],
            business_type=BusinessType(data['business_type']),
            description=data.get('description', ''),
            status=MerchantStatus.PENDING,
            is_active=False
        )
        
        db.session.add(merchant)
        db.session.flush()  # 获取merchant.id
        
        # 创建商家用户记录
        user = MerchantUser(
            merchant_id=merchant.id,
            username=data['username'],
            email=data['email'],
            phone=data['contact_phone'],
            password_hash=generate_password_hash(data['password']),
            role=MerchantUserRole.OWNER,
            permissions=['all'],
            is_active=True
        )
        
        db.session.add(user)
        db.session.commit()
        
        # 发送注册成功邮件（可选）
        try:
            send_email(
                to=data['email'],
                subject='商家入驻申请已提交',
                template='registration_success',
                merchant_name=data['merchant_name']
            )
        except Exception as e:
            current_app.logger.warning(f'发送注册邮件失败: {str(e)}')
        
        return jsonify({
            'success': True,
            'message': '注册申请提交成功，请等待审核',
            'data': {
                'merchant_id': merchant.id,
                'status': merchant.status.value
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'商家注册失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '注册失败，请稍后重试'
        }), 500

@bp.route('/merchant/login', methods=['POST'])
def merchant_login():
    """商家登录"""
    try:
        data = request.get_json()
        
        if not data.get('username') or not data.get('password'):
            return jsonify({
                'success': False,
                'message': '用户名和密码不能为空'
            }), 400
        
        # 查找用户
        user = MerchantUser.query.filter_by(
            username=data['username']
        ).first()
        
        if not user or not check_password_hash(user.password_hash, data['password']):
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
        
        # 检查用户状态
        if not user.is_active:
            return jsonify({
                'success': False,
                'message': '账户已被禁用，请联系客服'
            }), 403
        
        # 检查商家状态
        merchant = user.merchant
        if merchant.status != MerchantStatus.APPROVED:
            return jsonify({
                'success': False,
                'message': '商家审核未通过，无法登录',
                'data': {
                    'status': merchant.status.value,
                    'redirect': '/merchant-status'
                }
            }), 403
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # 生成JWT令牌
        access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'merchant_id': merchant.id,
                'role': user.role.value,
                'permissions': user.permissions
            }
        )
        refresh_token = create_refresh_token(identity=user.id)
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'data': {
                'user': {
                    'id': user.id,
                    'merchant_id': merchant.id,
                    'username': user.username,
                    'email': user.email,
                    'phone': user.phone,
                    'role': user.role.value,
                    'permissions': user.permissions,
                    'is_active': user.is_active,
                    'last_login': user.last_login.isoformat() if user.last_login else None
                },
                'merchant': {
                    'id': merchant.id,
                    'name': merchant.name,
                    'status': merchant.status.value,
                    'is_active': merchant.is_active,
                    'business_type': merchant.business_type.value
                },
                'access_token': access_token,
                'refresh_token': refresh_token
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'登录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '登录失败，请稍后重试'
        }), 500

@bp.route('/merchant/logout', methods=['POST'])
@jwt_required()
def merchant_logout():
    """商家退出登录"""
    try:
        # 这里可以将token加入黑名单
        # 简单实现，实际应该使用Redis存储黑名单
        
        return jsonify({
            'success': True,
            'message': '退出登录成功'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'退出登录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '退出登录失败'
        }), 500

@bp.route('/merchant/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取当前用户信息"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': {
                'id': user.id,
                'merchant_id': user.merchant_id,
                'username': user.username,
                'email': user.email,
                'phone': user.phone,
                'role': user.role.value,
                'permissions': user.permissions,
                'is_active': user.is_active,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'created_at': user.created_at.isoformat(),
                'merchant': {
                    'id': user.merchant.id,
                    'name': user.merchant.name,
                    'status': user.merchant.status.value,
                    'is_active': user.merchant.is_active
                }
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取用户信息失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取用户信息失败'
        }), 500

@bp.route('/merchant/status', methods=['GET'])
@jwt_required()
def get_merchant_status():
    """获取商家状态"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        merchant = user.merchant
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': {
                'status': merchant.status.value,
                'submitted_at': merchant.created_at.isoformat(),
                'reviewed_at': merchant.approved_at.isoformat() if merchant.approved_at else None,
                'reason': '审核中，请耐心等待' if merchant.status == MerchantStatus.PENDING else None,
                'next_steps': [
                    '等待工作人员审核',
                    '保持联系方式畅通',
                    '准备开店资料'
                ] if merchant.status == MerchantStatus.PENDING else []
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取商家状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取商家状态失败'
        }), 500

@bp.route('/send-code', methods=['POST'])
def send_verification_code():
    """发送验证码"""
    try:
        data = request.get_json()
        phone = data.get('phone')
        
        if not phone or not validate_phone(phone):
            return jsonify({
                'success': False,
                'message': '手机号格式不正确'
            }), 400
        
        # 生成6位验证码
        code = ''.join(random.choices(string.digits, k=6))
        
        # 存储验证码（5分钟有效）
        verification_codes[phone] = {
            'code': code,
            'expires_at': datetime.utcnow() + timedelta(minutes=5)
        }
        
        # 发送验证码（这里模拟发送成功）
        try:
            send_verification_code(phone, code)
        except Exception as e:
            current_app.logger.warning(f'发送验证码失败: {str(e)}')
        
        return jsonify({
            'success': True,
            'message': '验证码已发送',
            'data': {
                'code': code  # 开发环境返回验证码，生产环境应删除
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'发送验证码失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '发送验证码失败'
        }), 500

@bp.route('/merchant/login-code', methods=['POST'])
def login_with_code():
    """验证码登录"""
    try:
        data = request.get_json()
        phone = data.get('phone')
        code = data.get('code')
        
        if not phone or not code:
            return jsonify({
                'success': False,
                'message': '手机号和验证码不能为空'
            }), 400
        
        # 验证验证码
        stored_code = verification_codes.get(phone)
        if not stored_code:
            return jsonify({
                'success': False,
                'message': '验证码不存在或已过期'
            }), 400
        
        if stored_code['expires_at'] < datetime.utcnow():
            del verification_codes[phone]
            return jsonify({
                'success': False,
                'message': '验证码已过期'
            }), 400
        
        if stored_code['code'] != code:
            return jsonify({
                'success': False,
                'message': '验证码错误'
            }), 400
        
        # 查找用户
        user = MerchantUser.query.filter_by(phone=phone).first()
        if not user:
            return jsonify({
                'success': False,
                'message': '该手机号未注册'
            }), 404
        
        # 删除已使用的验证码
        del verification_codes[phone]
        
        # 检查用户和商家状态
        if not user.is_active:
            return jsonify({
                'success': False,
                'message': '账户已被禁用'
            }), 403
        
        merchant = user.merchant
        if merchant.status != MerchantStatus.APPROVED:
            return jsonify({
                'success': False,
                'message': '商家审核未通过，无法登录'
            }), 403
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # 生成JWT令牌
        access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'merchant_id': merchant.id,
                'role': user.role.value,
                'permissions': user.permissions
            }
        )
        refresh_token = create_refresh_token(identity=user.id)
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'data': {
                'user': user.to_dict(),
                'merchant': merchant.to_dict(),
                'access_token': access_token,
                'refresh_token': refresh_token
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'验证码登录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '登录失败，请稍后重试'
        }), 500

@bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新访问令牌"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user or not user.is_active:
            return jsonify({
                'success': False,
                'message': '用户不存在或已被禁用'
            }), 401
        
        # 生成新的访问令牌
        access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'merchant_id': user.merchant_id,
                'role': user.role.value,
                'permissions': user.permissions
            }
        )
        
        return jsonify({
            'success': True,
            'message': '令牌刷新成功',
            'data': {
                'access_token': access_token
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'刷新令牌失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '刷新令牌失败'
        }), 500
