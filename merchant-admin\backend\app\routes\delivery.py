#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配送管理相关路由
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

bp = Blueprint('delivery', __name__)

@bp.route('/persons', methods=['GET'])
@jwt_required()
def get_delivery_persons():
    """获取配送员列表"""
    try:
        # 模拟配送员数据
        persons = [
            {
                'id': 1,
                'name': '李师傅',
                'phone': '139****5678',
                'status': 'available',
                'current_orders': 2,
                'total_orders': 156,
                'rating': 4.9,
                'created_at': '2024-12-01 09:00:00'
            }
        ]
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': persons
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取配送员列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取配送员列表失败'
        }), 500

@bp.route('/areas', methods=['GET'])
@jwt_required()
def get_delivery_areas():
    """获取配送区域列表"""
    try:
        # 模拟配送区域数据
        areas = [
            {
                'id': 1,
                'name': '科技园片区',
                'description': '南山科技园及周边',
                'delivery_fee': 5.0,
                'min_order_amount': 20.0,
                'is_active': True
            }
        ]
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': areas
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取配送区域失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取配送区域失败'
        }), 500
