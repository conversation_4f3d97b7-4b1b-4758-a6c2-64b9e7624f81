#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家管理相关路由
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from datetime import datetime

from app import db
from app.models.merchant import Merchant, MerchantUser, MerchantStatus, BusinessType
from app.utils.validators import validate_email, validate_phone
from app.utils.helpers import paginate_query

bp = Blueprint('merchant', __name__)

@bp.route('/info', methods=['GET'])
@jwt_required()
def get_merchant_info():
    """获取商家信息"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        merchant = user.merchant
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': merchant.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取商家信息失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取商家信息失败'
        }), 500

@bp.route('/info', methods=['PUT'])
@jwt_required()
def update_merchant_info():
    """更新商家信息"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        merchant = user.merchant
        data = request.get_json()
        
        # 可更新的字段
        updatable_fields = [
            'name', 'contact_person', 'contact_phone', 'email',
            'province', 'city', 'district', 'address', 'description',
            'logo', 'banner_images', 'opening_hours', 'delivery_fee',
            'min_order_amount', 'delivery_radius'
        ]
        
        for field in updatable_fields:
            if field in data:
                if field == 'email' and data[field]:
                    if not validate_email(data[field]):
                        return jsonify({
                            'success': False,
                            'message': '邮箱格式不正确'
                        }), 400
                
                if field == 'contact_phone' and data[field]:
                    if not validate_phone(data[field]):
                        return jsonify({
                            'success': False,
                            'message': '手机号格式不正确'
                        }), 400
                
                setattr(merchant, field, data[field])
        
        merchant.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '更新成功',
            'data': merchant.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新商家信息失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '更新失败，请稍后重试'
        }), 500

@bp.route('/users', methods=['GET'])
@jwt_required()
def get_merchant_users():
    """获取商家用户列表"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 检查权限
        claims = get_jwt()
        if 'user_management' not in claims.get('permissions', []) and 'all' not in claims.get('permissions', []):
            return jsonify({
                'success': False,
                'message': '权限不足'
            }), 403
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        query = MerchantUser.query.filter_by(merchant_id=user.merchant_id)
        
        # 搜索
        search = request.args.get('search', '').strip()
        if search:
            query = query.filter(
                db.or_(
                    MerchantUser.username.contains(search),
                    MerchantUser.email.contains(search),
                    MerchantUser.phone.contains(search)
                )
            )
        
        # 角色筛选
        role = request.args.get('role')
        if role:
            query = query.filter(MerchantUser.role == role)
        
        # 状态筛选
        is_active = request.args.get('is_active')
        if is_active is not None:
            query = query.filter(MerchantUser.is_active == (is_active.lower() == 'true'))
        
        result = paginate_query(query, page, per_page)
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': [user.to_dict() for user in result['items']],
            'total': result['total'],
            'page': result['page'],
            'per_page': result['per_page']
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取用户列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取用户列表失败'
        }), 500

@bp.route('/users', methods=['POST'])
@jwt_required()
def create_merchant_user():
    """创建商家用户"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 检查权限
        claims = get_jwt()
        if 'user_management' not in claims.get('permissions', []) and 'all' not in claims.get('permissions', []):
            return jsonify({
                'success': False,
                'message': '权限不足'
            }), 403
        
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['username', 'email', 'password', 'role']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 验证邮箱格式
        if not validate_email(data['email']):
            return jsonify({
                'success': False,
                'message': '邮箱格式不正确'
            }), 400
        
        # 验证手机号格式
        if data.get('phone') and not validate_phone(data['phone']):
            return jsonify({
                'success': False,
                'message': '手机号格式不正确'
            }), 400
        
        # 检查用户名是否已存在
        existing_user = MerchantUser.query.filter_by(
            username=data['username']
        ).first()
        if existing_user:
            return jsonify({
                'success': False,
                'message': '用户名已存在'
            }), 400
        
        # 检查邮箱是否已存在
        existing_email = MerchantUser.query.filter_by(
            email=data['email']
        ).first()
        if existing_email:
            return jsonify({
                'success': False,
                'message': '邮箱已被使用'
            }), 400
        
        # 创建新用户
        from werkzeug.security import generate_password_hash
        
        new_user = MerchantUser(
            merchant_id=user.merchant_id,
            username=data['username'],
            email=data['email'],
            phone=data.get('phone', ''),
            password_hash=generate_password_hash(data['password']),
            role=data['role'],
            permissions=data.get('permissions', []),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '用户创建成功',
            'data': new_user.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建用户失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '创建用户失败'
        }), 500

@bp.route('/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_merchant_user(user_id):
    """更新商家用户"""
    try:
        current_user_id = get_jwt_identity()
        current_user = MerchantUser.query.get(current_user_id)
        
        if not current_user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 检查权限
        claims = get_jwt()
        if 'user_management' not in claims.get('permissions', []) and 'all' not in claims.get('permissions', []):
            return jsonify({
                'success': False,
                'message': '权限不足'
            }), 403
        
        # 查找要更新的用户
        target_user = MerchantUser.query.filter_by(
            id=user_id,
            merchant_id=current_user.merchant_id
        ).first()
        
        if not target_user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        data = request.get_json()
        
        # 可更新的字段
        updatable_fields = ['email', 'phone', 'role', 'permissions', 'is_active']
        
        for field in updatable_fields:
            if field in data:
                if field == 'email' and data[field]:
                    if not validate_email(data[field]):
                        return jsonify({
                            'success': False,
                            'message': '邮箱格式不正确'
                        }), 400
                    
                    # 检查邮箱是否被其他用户使用
                    existing_email = MerchantUser.query.filter(
                        MerchantUser.email == data[field],
                        MerchantUser.id != user_id
                    ).first()
                    if existing_email:
                        return jsonify({
                            'success': False,
                            'message': '邮箱已被其他用户使用'
                        }), 400
                
                if field == 'phone' and data[field]:
                    if not validate_phone(data[field]):
                        return jsonify({
                            'success': False,
                            'message': '手机号格式不正确'
                        }), 400
                
                setattr(target_user, field, data[field])
        
        target_user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '用户更新成功',
            'data': target_user.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新用户失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '更新用户失败'
        }), 500

@bp.route('/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_merchant_user(user_id):
    """删除商家用户"""
    try:
        current_user_id = get_jwt_identity()
        current_user = MerchantUser.query.get(current_user_id)
        
        if not current_user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 检查权限
        claims = get_jwt()
        if 'user_management' not in claims.get('permissions', []) and 'all' not in claims.get('permissions', []):
            return jsonify({
                'success': False,
                'message': '权限不足'
            }), 403
        
        # 不能删除自己
        if current_user_id == user_id:
            return jsonify({
                'success': False,
                'message': '不能删除自己的账户'
            }), 400
        
        # 查找要删除的用户
        target_user = MerchantUser.query.filter_by(
            id=user_id,
            merchant_id=current_user.merchant_id
        ).first()
        
        if not target_user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 不能删除店主账户
        if target_user.role.value == 'owner':
            return jsonify({
                'success': False,
                'message': '不能删除店主账户'
            }), 400
        
        db.session.delete(target_user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '用户删除成功'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除用户失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '删除用户失败'
        }), 500

@bp.route('/stats', methods=['GET'])
@jwt_required()
def get_merchant_stats():
    """获取商家统计数据"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        merchant = user.merchant
        
        # 这里应该从各个相关表中统计数据
        # 暂时返回模拟数据
        stats = {
            'total_orders': 1250,
            'pending_orders': 15,
            'today_revenue': 8650.50,
            'month_revenue': 125680.00,
            'total_products': 89,
            'low_stock_products': 5,
            'total_reviews': 456,
            'average_rating': 4.8,
            'total_customers': 2340,
            'active_customers': 890,
            'delivery_persons': 8,
            'active_delivery_persons': 6
        }
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': stats
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取统计数据失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取统计数据失败'
        }), 500
