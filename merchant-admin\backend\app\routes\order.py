#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单管理相关路由
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta

from app import db
from app.models.merchant import MerchantUser
from app.utils.helpers import paginate_query

bp = Blueprint('order', __name__)

@bp.route('/', methods=['GET'])
@jwt_required()
def get_orders():
    """获取订单列表"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 模拟订单数据
        orders = [
            {
                'id': 1,
                'order_number': 'ORD202412120001',
                'customer_name': '张三',
                'customer_phone': '138****1234',
                'total_amount': 68.50,
                'status': 'pending',
                'delivery_type': 'delivery',
                'delivery_address': '深圳市南山区科技园',
                'created_at': '2024-12-12 14:30:00',
                'items': [
                    {
                        'product_name': '宫保鸡丁',
                        'quantity': 1,
                        'unit_price': 28.00,
                        'subtotal': 28.00
                    },
                    {
                        'product_name': '麻婆豆腐',
                        'quantity': 2,
                        'unit_price': 18.00,
                        'subtotal': 36.00
                    }
                ]
            }
        ]
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': orders,
            'total': len(orders)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取订单列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取订单列表失败'
        }), 500

@bp.route('/<int:order_id>/status', methods=['PUT'])
@jwt_required()
def update_order_status(order_id):
    """更新订单状态"""
    try:
        data = request.get_json()
        status = data.get('status')
        
        if not status:
            return jsonify({
                'success': False,
                'message': '状态不能为空'
            }), 400
        
        # 这里应该更新数据库中的订单状态
        # 暂时返回成功响应
        
        return jsonify({
            'success': True,
            'message': '订单状态更新成功'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'更新订单状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '更新订单状态失败'
        }), 500
