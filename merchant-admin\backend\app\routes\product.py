#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品管理相关路由
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from datetime import datetime

from app import db
from app.models.merchant import MerchantUser
from app.models.product import Category, Product, ProductVariant, ProductStatus
from app.utils.validators import validate_price, validate_quantity, validate_sort_order
from app.utils.helpers import paginate_query

bp = Blueprint('product', __name__)

# 分类管理
@bp.route('/categories', methods=['GET'])
@jwt_required()
def get_categories():
    """获取分类列表"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        categories = Category.query.filter_by(
            merchant_id=user.merchant_id
        ).order_by(Category.sort_order.asc(), Category.id.asc()).all()
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': [category.to_dict() for category in categories]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取分类列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取分类列表失败'
        }), 500

@bp.route('/categories', methods=['POST'])
@jwt_required()
def create_category():
    """创建分类"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('name'):
            return jsonify({
                'success': False,
                'message': '分类名称不能为空'
            }), 400
        
        # 检查分类名称是否已存在
        existing_category = Category.query.filter_by(
            merchant_id=user.merchant_id,
            name=data['name']
        ).first()
        
        if existing_category:
            return jsonify({
                'success': False,
                'message': '分类名称已存在'
            }), 400
        
        # 创建分类
        category = Category(
            merchant_id=user.merchant_id,
            name=data['name'],
            description=data.get('description', ''),
            image=data.get('image', ''),
            sort_order=data.get('sort_order', 0),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(category)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '分类创建成功',
            'data': category.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建分类失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '创建分类失败'
        }), 500

@bp.route('/categories/<int:category_id>', methods=['PUT'])
@jwt_required()
def update_category(category_id):
    """更新分类"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        category = Category.query.filter_by(
            id=category_id,
            merchant_id=user.merchant_id
        ).first()
        
        if not category:
            return jsonify({
                'success': False,
                'message': '分类不存在'
            }), 404
        
        data = request.get_json()
        
        # 检查分类名称是否已被其他分类使用
        if 'name' in data and data['name'] != category.name:
            existing_category = Category.query.filter_by(
                merchant_id=user.merchant_id,
                name=data['name']
            ).first()
            
            if existing_category:
                return jsonify({
                    'success': False,
                    'message': '分类名称已存在'
                }), 400
        
        # 更新字段
        updatable_fields = ['name', 'description', 'image', 'sort_order', 'is_active']
        for field in updatable_fields:
            if field in data:
                setattr(category, field, data[field])
        
        category.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '分类更新成功',
            'data': category.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新分类失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '更新分类失败'
        }), 500

@bp.route('/categories/<int:category_id>', methods=['DELETE'])
@jwt_required()
def delete_category(category_id):
    """删除分类"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        category = Category.query.filter_by(
            id=category_id,
            merchant_id=user.merchant_id
        ).first()
        
        if not category:
            return jsonify({
                'success': False,
                'message': '分类不存在'
            }), 404
        
        # 检查是否有商品使用此分类
        product_count = Product.query.filter_by(category_id=category_id).count()
        if product_count > 0:
            return jsonify({
                'success': False,
                'message': f'该分类下还有{product_count}个商品，无法删除'
            }), 400
        
        db.session.delete(category)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '分类删除成功'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除分类失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '删除分类失败'
        }), 500

@bp.route('/categories/sort', methods=['PUT'])
@jwt_required()
def update_categories_sort():
    """更新分类排序"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        data = request.get_json()
        items = data.get('items', [])
        
        if not items:
            return jsonify({
                'success': False,
                'message': '排序数据不能为空'
            }), 400
        
        # 批量更新排序
        for item in items:
            category_id = item.get('id')
            sort_order = item.get('sort_order')
            
            if category_id and sort_order is not None:
                category = Category.query.filter_by(
                    id=category_id,
                    merchant_id=user.merchant_id
                ).first()
                
                if category:
                    category.sort_order = sort_order
                    category.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '排序更新成功'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新排序失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '更新排序失败'
        }), 500

# 商品管理
@bp.route('/', methods=['GET'])
@jwt_required()
def get_products():
    """获取商品列表"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        query = Product.query.filter_by(merchant_id=user.merchant_id)
        
        # 分类筛选
        category_id = request.args.get('category_id', type=int)
        if category_id:
            query = query.filter(Product.category_id == category_id)
        
        # 状态筛选
        status = request.args.get('status')
        if status:
            query = query.filter(Product.status == status)
        
        # 搜索
        search = request.args.get('search', '').strip()
        if search:
            query = query.filter(
                db.or_(
                    Product.name.contains(search),
                    Product.description.contains(search)
                )
            )
        
        # 排序
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        if hasattr(Product, sort_by):
            if sort_order == 'asc':
                query = query.order_by(getattr(Product, sort_by).asc())
            else:
                query = query.order_by(getattr(Product, sort_by).desc())
        
        result = paginate_query(query, page, per_page)
        
        # 添加分类名称
        products_data = []
        for product in result['items']:
            product_dict = product.to_dict()
            if product.category:
                product_dict['category_name'] = product.category.name
            products_data.append(product_dict)
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': products_data,
            'total': result['total'],
            'page': result['page'],
            'per_page': result['per_page']
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取商品列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取商品列表失败'
        }), 500

@bp.route('/', methods=['POST'])
@jwt_required()
def create_product():
    """创建商品"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'category_id', 'price']
        for field in required_fields:
            if field not in data or data[field] is None:
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 验证分类是否存在
        category = Category.query.filter_by(
            id=data['category_id'],
            merchant_id=user.merchant_id
        ).first()
        
        if not category:
            return jsonify({
                'success': False,
                'message': '分类不存在'
            }), 404
        
        # 验证价格
        if not validate_price(data['price']):
            return jsonify({
                'success': False,
                'message': '价格格式不正确'
            }), 400
        
        # 创建商品
        product = Product(
            merchant_id=user.merchant_id,
            category_id=data['category_id'],
            name=data['name'],
            description=data.get('description', ''),
            images=data.get('images', []),
            price=data['price'],
            original_price=data.get('original_price'),
            cost_price=data.get('cost_price'),
            stock_quantity=data.get('stock_quantity', 0),
            min_stock=data.get('min_stock', 0),
            max_stock=data.get('max_stock', 999),
            unit=data.get('unit', '份'),
            weight=data.get('weight'),
            calories=data.get('calories'),
            ingredients=data.get('ingredients', ''),
            allergens=data.get('allergens', ''),
            tags=data.get('tags', []),
            is_spicy=data.get('is_spicy', False),
            is_vegetarian=data.get('is_vegetarian', False),
            is_recommended=data.get('is_recommended', False),
            is_new=data.get('is_new', False),
            status=ProductStatus(data.get('status', 'active')),
            sort_order=data.get('sort_order', 0)
        )
        
        db.session.add(product)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '商品创建成功',
            'data': product.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建商品失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '创建商品失败'
        }), 500

@bp.route('/<int:product_id>', methods=['GET'])
@jwt_required()
def get_product(product_id):
    """获取商品详情"""
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        product = Product.query.filter_by(
            id=product_id,
            merchant_id=user.merchant_id
        ).first()
        
        if not product:
            return jsonify({
                'success': False,
                'message': '商品不存在'
            }), 404
        
        product_dict = product.to_dict()
        
        # 添加分类信息
        if product.category:
            product_dict['category_name'] = product.category.name
        
        # 添加规格信息
        variants = ProductVariant.query.filter_by(
            product_id=product_id
        ).order_by(ProductVariant.sort_order.asc()).all()
        product_dict['variants'] = [variant.to_dict() for variant in variants]
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': product_dict
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取商品详情失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取商品详情失败'
        }), 500
