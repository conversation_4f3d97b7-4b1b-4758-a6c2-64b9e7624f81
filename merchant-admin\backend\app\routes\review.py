#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评价管理相关路由
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

bp = Blueprint('review', __name__)

@bp.route('/list', methods=['GET'])
@jwt_required()
def get_reviews():
    """获取评价列表"""
    try:
        # 模拟评价数据
        reviews = [
            {
                'id': 1,
                'order_number': 'ORD202412120001',
                'customer_name': '张三',
                'product_name': '宫保鸡丁',
                'rating': 5,
                'content': '味道很好，分量足',
                'images': [],
                'reply': '',
                'created_at': '2024-12-12 16:30:00'
            }
        ]
        
        return jsonify({
            'success': True,
            'message': '获取成功',
            'data': reviews
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'获取评价列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取评价列表失败'
        }), 500

@bp.route('/<int:review_id>/reply', methods=['POST'])
@jwt_required()
def reply_review(review_id):
    """回复评价"""
    try:
        data = request.get_json()
        reply = data.get('reply', '')
        
        return jsonify({
            'success': True,
            'message': '回复成功'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'回复评价失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '回复评价失败'
        }), 500
