#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传相关路由
"""

import os
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename

from app.utils.validators import validate_image_file
from app.utils.helpers import generate_unique_filename, upload_file_to_storage

bp = Blueprint('upload', __name__)

@bp.route('/image', methods=['POST'])
@jwt_required()
def upload_image():
    """上传图片"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        # 验证文件类型
        if not validate_image_file(file.filename):
            return jsonify({
                'success': False,
                'message': '不支持的文件类型'
            }), 400
        
        # 生成唯一文件名
        filename = generate_unique_filename(file.filename)
        
        # 保存文件
        upload_folder = current_app.config['UPLOAD_FOLDER']
        images_folder = os.path.join(upload_folder, 'images')
        
        if not os.path.exists(images_folder):
            os.makedirs(images_folder)
        
        file_path = os.path.join(images_folder, filename)
        file.save(file_path)
        
        # 生成访问URL
        file_url = f'/uploads/images/{filename}'
        
        return jsonify({
            'success': True,
            'message': '上传成功',
            'data': {
                'url': file_url,
                'filename': filename
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'上传图片失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '上传失败，请稍后重试'
        }), 500

@bp.route('/document', methods=['POST'])
@jwt_required()
def upload_document():
    """上传文档"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        # 生成唯一文件名
        filename = generate_unique_filename(file.filename)
        
        # 保存文件
        upload_folder = current_app.config['UPLOAD_FOLDER']
        documents_folder = os.path.join(upload_folder, 'documents')
        
        if not os.path.exists(documents_folder):
            os.makedirs(documents_folder)
        
        file_path = os.path.join(documents_folder, filename)
        file.save(file_path)
        
        # 生成访问URL
        file_url = f'/uploads/documents/{filename}'
        
        return jsonify({
            'success': True,
            'message': '上传成功',
            'data': {
                'url': file_url,
                'filename': filename
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'上传文档失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '上传失败，请稍后重试'
        }), 500
