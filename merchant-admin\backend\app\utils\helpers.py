#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
辅助工具函数
"""

import os
import uuid
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from flask import current_app
import json

def generate_unique_filename(original_filename: str) -> str:
    """生成唯一文件名"""
    if not original_filename:
        return str(uuid.uuid4())
    
    # 获取文件扩展名
    ext = ''
    if '.' in original_filename:
        ext = '.' + original_filename.rsplit('.', 1)[1].lower()
    
    # 生成唯一文件名
    unique_name = str(uuid.uuid4()) + ext
    return unique_name

def generate_order_number() -> str:
    """生成订单号"""
    import time
    timestamp = str(int(time.time()))
    random_str = str(uuid.uuid4().hex)[:6].upper()
    return f"ORD{timestamp}{random_str}"

def generate_refund_number() -> str:
    """生成退款单号"""
    import time
    timestamp = str(int(time.time()))
    random_str = str(uuid.uuid4().hex)[:6].upper()
    return f"REF{timestamp}{random_str}"

def generate_ticket_number() -> str:
    """生成工单号"""
    import time
    timestamp = str(int(time.time()))
    random_str = str(uuid.uuid4().hex)[:6].upper()
    return f"CS{timestamp}{random_str}"

def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """计算两点间距离（公里）"""
    import math
    
    # 将度数转换为弧度
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
    
    # Haversine公式
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # 地球半径（公里）
    r = 6371
    
    return c * r

def format_currency(amount: float) -> str:
    """格式化货币"""
    return f"¥{amount:.2f}"

def format_datetime(dt: datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """格式化日期时间"""
    if not dt:
        return ''
    return dt.strftime(format_str)

def parse_datetime(dt_str: str) -> Optional[datetime]:
    """解析日期时间字符串"""
    if not dt_str:
        return None
    
    formats = [
        '%Y-%m-%d %H:%M:%S',
        '%Y-%m-%d',
        '%Y-%m-%dT%H:%M:%S',
        '%Y-%m-%dT%H:%M:%SZ',
        '%Y-%m-%dT%H:%M:%S.%fZ'
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(dt_str, fmt)
        except ValueError:
            continue
    
    return None

def send_verification_code(phone: str, code: str) -> bool:
    """发送验证码短信"""
    try:
        # 这里应该集成真实的短信服务商API
        # 如阿里云短信、腾讯云短信等
        current_app.logger.info(f'发送验证码到 {phone}: {code}')
        return True
    except Exception as e:
        current_app.logger.error(f'发送验证码失败: {str(e)}')
        return False

def send_email(to: str, subject: str, template: str, **kwargs) -> bool:
    """发送邮件"""
    try:
        # 这里应该集成邮件服务
        current_app.logger.info(f'发送邮件到 {to}: {subject}')
        return True
    except Exception as e:
        current_app.logger.error(f'发送邮件失败: {str(e)}')
        return False

def upload_file_to_storage(file_path: str, storage_type: str = 'local') -> str:
    """上传文件到存储服务"""
    try:
        if storage_type == 'local':
            # 本地存储，返回相对路径
            return file_path
        elif storage_type == 'oss':
            # 阿里云OSS
            # 这里应该实现OSS上传逻辑
            return f"https://your-bucket.oss-cn-hangzhou.aliyuncs.com/{file_path}"
        elif storage_type == 'qiniu':
            # 七牛云
            # 这里应该实现七牛云上传逻辑
            return f"https://your-domain.qiniucdn.com/{file_path}"
        else:
            return file_path
    except Exception as e:
        current_app.logger.error(f'文件上传失败: {str(e)}')
        return file_path

def generate_file_hash(file_path: str) -> str:
    """生成文件哈希值"""
    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception:
        return ''

def compress_image(image_path: str, max_size: tuple = (800, 600), quality: int = 85) -> str:
    """压缩图片"""
    try:
        from PIL import Image
        
        with Image.open(image_path) as img:
            # 转换为RGB模式（如果是RGBA）
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 计算新尺寸
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 生成压缩后的文件名
            name, ext = os.path.splitext(image_path)
            compressed_path = f"{name}_compressed{ext}"
            
            # 保存压缩后的图片
            img.save(compressed_path, 'JPEG', quality=quality, optimize=True)
            
            return compressed_path
    except Exception as e:
        current_app.logger.error(f'图片压缩失败: {str(e)}')
        return image_path

def paginate_query(query, page: int, per_page: int) -> Dict[str, Any]:
    """分页查询"""
    try:
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return {
            'items': pagination.items,
            'total': pagination.total,
            'page': pagination.page,
            'per_page': pagination.per_page,
            'pages': pagination.pages,
            'has_prev': pagination.has_prev,
            'has_next': pagination.has_next,
            'prev_num': pagination.prev_num,
            'next_num': pagination.next_num
        }
    except Exception as e:
        current_app.logger.error(f'分页查询失败: {str(e)}')
        return {
            'items': [],
            'total': 0,
            'page': 1,
            'per_page': per_page,
            'pages': 0,
            'has_prev': False,
            'has_next': False,
            'prev_num': None,
            'next_num': None
        }

def cache_key(prefix: str, *args) -> str:
    """生成缓存键"""
    key_parts = [prefix] + [str(arg) for arg in args]
    return ':'.join(key_parts)

def serialize_datetime(dt: datetime) -> str:
    """序列化日期时间"""
    if not dt:
        return None
    return dt.isoformat()

def deserialize_datetime(dt_str: str) -> datetime:
    """反序列化日期时间"""
    if not dt_str:
        return None
    return datetime.fromisoformat(dt_str)

def safe_json_loads(json_str: str, default=None):
    """安全的JSON解析"""
    try:
        return json.loads(json_str) if json_str else default
    except (json.JSONDecodeError, TypeError):
        return default

def safe_json_dumps(obj, default=None) -> str:
    """安全的JSON序列化"""
    try:
        return json.dumps(obj, ensure_ascii=False, default=str)
    except (TypeError, ValueError):
        return json.dumps(default) if default is not None else '{}'

def mask_phone(phone: str) -> str:
    """手机号脱敏"""
    if not phone or len(phone) < 11:
        return phone
    return phone[:3] + '****' + phone[-4:]

def mask_email(email: str) -> str:
    """邮箱脱敏"""
    if not email or '@' not in email:
        return email
    
    local, domain = email.split('@', 1)
    if len(local) <= 2:
        return email
    
    masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
    return f"{masked_local}@{domain}"

def generate_random_string(length: int = 8) -> str:
    """生成随机字符串"""
    import random
    import string
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def is_business_hours(business_hours: Dict[str, Any]) -> bool:
    """检查是否在营业时间内"""
    if not business_hours:
        return True
    
    now = datetime.now()
    weekday = now.strftime('%A').lower()  # monday, tuesday, etc.
    current_time = now.strftime('%H:%M')
    
    day_hours = business_hours.get(weekday)
    if not day_hours or not day_hours.get('is_open', True):
        return False
    
    open_time = day_hours.get('open_time', '00:00')
    close_time = day_hours.get('close_time', '23:59')
    
    return open_time <= current_time <= close_time

def calculate_delivery_fee(distance: float, base_fee: float = 5.0, per_km_fee: float = 2.0) -> float:
    """计算配送费"""
    if distance <= 3:  # 3公里内基础配送费
        return base_fee
    else:
        extra_distance = distance - 3
        return base_fee + (extra_distance * per_km_fee)

def estimate_delivery_time(distance: float, base_time: int = 30) -> int:
    """估算配送时间（分钟）"""
    # 基础时间 + 距离时间（每公里2分钟）
    return base_time + int(distance * 2)

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"
