#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证工具函数
"""

import re
from typing import Optional

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str) -> bool:
    """验证手机号格式（中国大陆）"""
    if not phone:
        return False
    
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))

def validate_password(password: str) -> bool:
    """验证密码强度"""
    if not password:
        return False
    
    # 长度6-20位
    if len(password) < 6 or len(password) > 20:
        return False
    
    # 必须包含字母和数字
    has_letter = bool(re.search(r'[a-zA-Z]', password))
    has_digit = bool(re.search(r'\d', password))
    
    return has_letter and has_digit

def validate_business_license(license_no: str) -> bool:
    """验证营业执照号格式"""
    if not license_no:
        return False
    
    # 统一社会信用代码：18位，第1位为数字或大写字母，其余为数字或大写字母
    pattern = r'^[0-9A-Z]{18}$'
    return bool(re.match(pattern, license_no))

def validate_id_card(id_card: str) -> bool:
    """验证身份证号格式"""
    if not id_card:
        return False
    
    # 18位身份证号
    pattern = r'^\d{17}[\dXx]$'
    return bool(re.match(pattern, id_card))

def validate_price(price: float) -> bool:
    """验证价格格式"""
    if price is None:
        return False
    
    return price >= 0 and price <= 99999.99

def validate_quantity(quantity: int) -> bool:
    """验证数量格式"""
    if quantity is None:
        return False
    
    return quantity >= 0 and quantity <= 999999

def validate_coordinates(latitude: float, longitude: float) -> bool:
    """验证经纬度坐标"""
    if latitude is None or longitude is None:
        return False
    
    # 纬度范围：-90 到 90
    # 经度范围：-180 到 180
    return (-90 <= latitude <= 90) and (-180 <= longitude <= 180)

def validate_sort_order(sort_order: int) -> bool:
    """验证排序值"""
    if sort_order is None:
        return False
    
    return 0 <= sort_order <= 9999

def validate_rating(rating: float) -> bool:
    """验证评分"""
    if rating is None:
        return False
    
    return 1.0 <= rating <= 5.0

def validate_file_extension(filename: str, allowed_extensions: set) -> bool:
    """验证文件扩展名"""
    if not filename:
        return False
    
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def validate_image_file(filename: str) -> bool:
    """验证图片文件"""
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return validate_file_extension(filename, allowed_extensions)

def validate_document_file(filename: str) -> bool:
    """验证文档文件"""
    allowed_extensions = {'pdf', 'doc', 'docx', 'txt'}
    return validate_file_extension(filename, allowed_extensions)

def sanitize_string(text: str, max_length: Optional[int] = None) -> str:
    """清理字符串"""
    if not text:
        return ''
    
    # 去除首尾空格
    text = text.strip()
    
    # 限制长度
    if max_length and len(text) > max_length:
        text = text[:max_length]
    
    return text

def validate_pagination_params(page: int, per_page: int) -> tuple:
    """验证分页参数"""
    # 页码最小为1
    if page < 1:
        page = 1
    
    # 每页数量限制在1-100之间
    if per_page < 1:
        per_page = 20
    elif per_page > 100:
        per_page = 100
    
    return page, per_page

def validate_search_query(query: str) -> str:
    """验证搜索查询"""
    if not query:
        return ''
    
    # 去除特殊字符，只保留中文、英文、数字和空格
    pattern = r'[^\u4e00-\u9fa5a-zA-Z0-9\s]'
    query = re.sub(pattern, '', query)
    
    # 限制长度
    if len(query) > 50:
        query = query[:50]
    
    return query.strip()

def validate_date_range(start_date: str, end_date: str) -> tuple:
    """验证日期范围"""
    from datetime import datetime, timedelta
    
    try:
        start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # 开始日期不能大于结束日期
        if start > end:
            start, end = end, start
        
        # 日期范围不能超过1年
        if (end - start).days > 365:
            end = start + timedelta(days=365)
        
        return start, end
        
    except (ValueError, AttributeError):
        # 如果日期格式错误，返回默认范围（最近30天）
        end = datetime.utcnow()
        start = end - timedelta(days=30)
        return start, end

def validate_color_hex(color: str) -> bool:
    """验证十六进制颜色值"""
    if not color:
        return False
    
    pattern = r'^#[0-9A-Fa-f]{6}$'
    return bool(re.match(pattern, color))

def validate_url(url: str) -> bool:
    """验证URL格式"""
    if not url:
        return False
    
    pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    return bool(re.match(pattern, url))

def validate_json_field(data: dict, max_size: int = 1000) -> bool:
    """验证JSON字段"""
    if not isinstance(data, dict):
        return False
    
    import json
    try:
        json_str = json.dumps(data, ensure_ascii=False)
        return len(json_str) <= max_size
    except (TypeError, ValueError):
        return False
