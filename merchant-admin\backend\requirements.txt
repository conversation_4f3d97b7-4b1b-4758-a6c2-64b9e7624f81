# Flask核心框架
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0

# 数据库
SQLAlchemy==2.0.23
psycopg2-binary==2.9.7
PyMySQL==1.1.0

# 密码加密
Werkzeug==2.3.7
bcrypt==4.0.1

# 图片处理
Pillow==10.0.1

# 日期时间处理
python-dateutil==2.8.2

# 数据验证
marshmallow==3.20.1
email-validator==2.1.0

# HTTP请求
requests==2.31.0

# 环境变量
python-dotenv==1.0.0

# 开发工具
pytest==7.4.3
pytest-flask==1.3.0
coverage==7.3.2

# 生产环境
gunicorn==21.2.0
gevent==23.9.1

# 工具库
click==8.1.7
itsdangerous==2.1.2
MarkupSafe==2.1.3
Jinja2==3.1.2

# 数据处理
pandas==2.1.3
numpy==1.25.2

# 缓存
redis==5.0.1
Flask-Caching==2.1.0

# 任务队列
celery==5.3.4

# 文件处理
openpyxl==3.1.2
xlsxwriter==3.1.9

# API文档
flask-restx==1.2.0
flasgger==0.9.7.1

# 监控
flask-monitoring-dashboard==3.1.0

# 安全
flask-talisman==1.1.0
flask-limiter==3.5.0
