#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的后端测试应用
用于快速启动和测试后端服务
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os

# 创建Flask应用
app = Flask(__name__)

# 配置
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///merchant_admin.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = 'jwt-secret-string'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# 初始化扩展
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app)

# 简化的数据模型
class Merchant(db.Model):
    __tablename__ = 'merchants'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    business_name = db.Column(db.String(200), nullable=False)
    business_license = db.Column(db.String(50), unique=True, nullable=False)
    contact_person = db.Column(db.String(50), nullable=False)
    contact_phone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(100), nullable=False)
    province = db.Column(db.String(50), nullable=False)
    city = db.Column(db.String(50), nullable=False)
    district = db.Column(db.String(50), nullable=False)
    address = db.Column(db.String(200), nullable=False)
    business_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='approved')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class MerchantUser(db.Model):
    __tablename__ = 'merchant_users'
    id = db.Column(db.Integer, primary_key=True)
    merchant_id = db.Column(db.Integer, db.ForeignKey('merchants.id'), nullable=False)
    username = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    merchant = db.relationship('Merchant', backref='users')

# API路由
@app.route('/')
def index():
    return jsonify({
        'success': True,
        'message': '商家管理系统后端API',
        'version': '1.0.0',
        'endpoints': {
            'health': '/health',
            'login': '/api/auth/merchant/login',
            'merchant_info': '/api/merchant/info'
        }
    })

@app.route('/health')
def health_check():
    return jsonify({
        'success': True,
        'message': '服务正常',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/auth/merchant/login', methods=['POST'])
def merchant_login():
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'message': '用户名和密码不能为空'
            }), 400
        
        # 查找用户
        user = MerchantUser.query.filter_by(username=username).first()
        
        if not user or not check_password_hash(user.password_hash, password):
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
        
        # 检查商家状态
        merchant = user.merchant
        if merchant.status != 'approved':
            return jsonify({
                'success': False,
                'message': '商家审核未通过，无法登录',
                'data': {'status': merchant.status}
            }), 403
        
        # 生成token
        access_token = create_access_token(identity=user.id)
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'data': {
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'role': user.role
                },
                'merchant': {
                    'id': merchant.id,
                    'name': merchant.name,
                    'business_name': merchant.business_name,
                    'status': merchant.status
                },
                'access_token': access_token
            }
        })
        
    except Exception as e:
        app.logger.error(f'登录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '登录失败，请稍后重试'
        }), 500

@app.route('/api/merchant/info', methods=['GET'])
@jwt_required()
def get_merchant_info():
    try:
        user_id = get_jwt_identity()
        user = MerchantUser.query.get(user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        merchant = user.merchant
        
        return jsonify({
            'success': True,
            'message': '获取商家信息成功',
            'data': {
                'merchant': {
                    'id': merchant.id,
                    'name': merchant.name,
                    'business_name': merchant.business_name,
                    'business_license': merchant.business_license,
                    'contact_person': merchant.contact_person,
                    'contact_phone': merchant.contact_phone,
                    'email': merchant.email,
                    'province': merchant.province,
                    'city': merchant.city,
                    'district': merchant.district,
                    'address': merchant.address,
                    'business_type': merchant.business_type,
                    'description': merchant.description,
                    'status': merchant.status,
                    'is_active': merchant.is_active
                }
            }
        })
        
    except Exception as e:
        app.logger.error(f'获取商家信息失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取商家信息失败'
        }), 500

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': '接口不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'message': '服务器内部错误'
    }), 500

# 初始化数据库
def init_database():
    """初始化数据库"""
    try:
        with app.app_context():
            db.create_all()
            print("✅ 数据库表创建成功")
            
            # 创建默认商家
            default_merchant = Merchant.query.filter_by(business_license='DEFAULT001').first()
            if not default_merchant:
                default_merchant = Merchant(
                    name='演示商家',
                    business_name='演示餐厅',
                    business_license='DEFAULT001',
                    contact_person='管理员',
                    contact_phone='***********',
                    email='<EMAIL>',
                    province='北京市',
                    city='北京市',
                    district='朝阳区',
                    address='演示地址123号',
                    business_type='restaurant',
                    description='这是一个演示商家账户',
                    status='approved',
                    is_active=True
                )
                db.session.add(default_merchant)
                db.session.commit()
                print("✅ 默认商家创建成功")
            
            # 创建默认管理员
            default_user = MerchantUser.query.filter_by(username='admin').first()
            if not default_user:
                default_user = MerchantUser(
                    merchant_id=default_merchant.id,
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('123456'),
                    role='owner',
                    is_active=True
                )
                db.session.add(default_user)
                db.session.commit()
                print("✅ 默认管理员创建成功: admin/123456")
                
            return True
            
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False

if __name__ == '__main__':
    print("="*50)
    print("🚀 启动商家管理系统后端服务（测试版）")
    print("="*50)
    print(f"📍 服务地址: http://localhost:5001")
    print(f"🔧 健康检查: http://localhost:5001/health")
    print(f"🔑 登录接口: http://localhost:5001/api/auth/merchant/login")
    print(f"👤 演示账户: admin / 123456")
    print("="*50)
    
    # 初始化数据库
    init_database()
    
    print("🎉 服务启动中...")
    app.run(host='0.0.0.0', port=5001, debug=True)
