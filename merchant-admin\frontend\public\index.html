<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1890ff" />
    <meta name="description" content="商家管理系统 - 专业的餐饮商家管理平台" />
    <meta name="keywords" content="商家管理,餐饮管理,订单管理,菜品管理" />
    <meta name="author" content="商家管理系统" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="商家管理系统" />
    <meta property="og:description" content="专业的餐饮商家管理平台" />
    <meta property="og:image" content="%PUBLIC_URL%/logo192.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="商家管理系统" />
    <meta property="twitter:description" content="专业的餐饮商家管理平台" />
    <meta property="twitter:image" content="%PUBLIC_URL%/logo192.png" />
    
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 防止FOUC (Flash of Unstyled Content) -->
    <style>
      #root {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }
      
      #root.loaded {
        opacity: 1;
      }
      
      /* 初始加载动画 */
      .initial-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        color: white;
        font-size: 16px;
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    
    <title>商家管理系统</title>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h2>需要启用JavaScript</h2>
        <p>此应用需要JavaScript才能正常运行，请在浏览器中启用JavaScript。</p>
      </div>
    </noscript>
    
    <!-- 初始加载动画 -->
    <div id="initial-loading" class="initial-loading">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
    </div>
    
    <div id="root"></div>
    
    <!-- 移除初始加载动画的脚本 -->
    <script>
      window.addEventListener('load', function() {
        const initialLoading = document.getElementById('initial-loading');
        const root = document.getElementById('root');
        
        if (initialLoading) {
          setTimeout(() => {
            initialLoading.style.opacity = '0';
            setTimeout(() => {
              initialLoading.remove();
              root.classList.add('loaded');
            }, 300);
          }, 500);
        }
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('全局错误:', e.error);
      });
      
      window.addEventListener('unhandledrejection', function(e) {
        console.error('未处理的Promise拒绝:', e.reason);
      });
    </script>
  </body>
</html>
