/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: #262626;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

.App {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 文本选择样式 */
::selection {
  background: #bae7ff;
  color: #262626;
}

::-moz-selection {
  background: #bae7ff;
  color: #262626;
}

/* 链接样式 */
a {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: #40a9ff;
}

a:active {
  color: #096dd9;
}

/* 按钮全局样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-btn:hover {
  transform: translateY(-1px);
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 卡片全局样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

/* 表单全局样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector,
.ant-input-number {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-affix-wrapper-focused,
.ant-select-focused .ant-select-selector,
.ant-input-number-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格全局样式 */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 标签全局样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

/* 步骤条全局样式 */
.ant-steps-item-process .ant-steps-item-icon {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-steps-item-finish .ant-steps-item-icon {
  background: #52c41a;
  border-color: #52c41a;
}

.ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
  color: white;
}

/* 消息提示全局样式 */
.ant-message {
  z-index: 9999;
}

.ant-message-notice {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 模态框全局样式 */
.ant-modal {
  border-radius: 8px;
  overflow: hidden;
}

.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
}

/* 抽屉全局样式 */
.ant-drawer-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-drawer-title {
  font-weight: 600;
  color: #262626;
}

/* 下拉菜单全局样式 */
.ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.ant-dropdown-menu-item:hover {
  background: #f5f5f5;
}

/* 工具提示全局样式 */
.ant-tooltip-inner {
  border-radius: 6px;
  font-size: 12px;
}

/* 加载状态全局样式 */
.ant-spin-dot-item {
  background-color: #1890ff;
}

.ant-spin-text {
  color: #595959;
}

/* 分页全局样式 */
.ant-pagination {
  text-align: center;
  margin-top: 24px;
}

.ant-pagination-item {
  border-radius: 4px;
}

.ant-pagination-item-active {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-pagination-item-active a {
  color: white;
}

/* 面包屑全局样式 */
.ant-breadcrumb {
  margin-bottom: 16px;
}

.ant-breadcrumb a {
  color: #595959;
}

.ant-breadcrumb a:hover {
  color: #1890ff;
}

/* 警告框全局样式 */
.ant-alert {
  border-radius: 6px;
  border: none;
}

.ant-alert-info {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.ant-alert-success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.ant-alert-warning {
  background: #fffbe6;
  border: 1px solid #ffe58f;
}

.ant-alert-error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
}

/* 统计数值全局样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

/* 时间线全局样式 */
.ant-timeline-item-head {
  background: white;
  border: 2px solid;
}

.ant-timeline-item-tail {
  border-left: 2px solid #f0f0f0;
}

/* 响应式工具类 */
.mobile-hidden {
  display: block;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-only {
    display: block;
  }
}

/* 文本工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 间距工具类 */
.mb-0 { margin-bottom: 0 !important; }
.mb-8 { margin-bottom: 8px !important; }
.mb-16 { margin-bottom: 16px !important; }
.mb-24 { margin-bottom: 24px !important; }
.mb-32 { margin-bottom: 32px !important; }

.mt-0 { margin-top: 0 !important; }
.mt-8 { margin-top: 8px !important; }
.mt-16 { margin-top: 16px !important; }
.mt-24 { margin-top: 24px !important; }
.mt-32 { margin-top: 32px !important; }

/* 显示工具类 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

/* Flex工具类 */
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-end { justify-content: flex-end !important; }
.align-center { align-items: center !important; }
.align-start { align-items: flex-start !important; }
.align-end { align-items: flex-end !important; }

/* 颜色工具类 */
.text-primary { color: #1890ff !important; }
.text-success { color: #52c41a !important; }
.text-warning { color: #fa8c16 !important; }
.text-danger { color: #ff4d4f !important; }
.text-secondary { color: #8c8c8c !important; }

/* 字体工具类 */
.font-weight-normal { font-weight: 400 !important; }
.font-weight-medium { font-weight: 500 !important; }
.font-weight-semibold { font-weight: 600 !important; }
.font-weight-bold { font-weight: 700 !important; }
