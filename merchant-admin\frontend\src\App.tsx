import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import MainLayout from './components/Layout/MainLayout';

// 页面组件
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import RegisterSuccessPage from './pages/RegisterSuccessPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import MerchantStatusPage from './pages/MerchantStatusPage';
import Dashboard from './pages/Dashboard';

// 菜品管理页面
import CategoriesPage from './pages/products/CategoriesPage';

// 错误页面
import UnauthorizedPage from './pages/UnauthorizedPage';
import NotFoundPage from './pages/NotFoundPage';

import './App.css';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* 公开路由 */}
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/register-success" element={<RegisterSuccessPage />} />
              <Route path="/forgot-password" element={<ForgotPasswordPage />} />
              
              {/* 商家状态页面 */}
              <Route path="/merchant-status" element={<MerchantStatusPage />} />
              
              {/* 错误页面 */}
              <Route path="/unauthorized" element={<UnauthorizedPage />} />
              <Route path="/404" element={<NotFoundPage />} />
              
              {/* 受保护的路由 */}
              <Route path="/" element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }>
                {/* 仪表盘 */}
                <Route index element={<Navigate to="/dashboard" replace />} />
                <Route path="dashboard" element={<Dashboard />} />
                
                {/* 菜品管理 */}
                <Route path="products">
                  <Route path="categories" element={<CategoriesPage />} />
                  <Route path="list" element={<div>菜品列表</div>} />
                  <Route path="add" element={<div>添加菜品</div>} />
                  <Route path="edit/:id" element={<div>编辑菜品</div>} />
                </Route>
                
                {/* 订单管理 */}
                <Route path="orders">
                  <Route path="list" element={<div>订单列表</div>} />
                  <Route path="pending" element={<div>待处理订单</div>} />
                  <Route path="history" element={<div>历史订单</div>} />
                </Route>
                
                {/* 售后管理 */}
                <Route path="refunds">
                  <Route path="list" element={<div>退款申请</div>} />
                  <Route path="tickets" element={<div>客服工单</div>} />
                </Route>
                
                {/* 评价管理 */}
                <Route path="reviews">
                  <Route path="list" element={<div>评价列表</div>} />
                  <Route path="reply" element={<div>待回复评价</div>} />
                </Route>
                
                {/* 配送管理 */}
                <Route path="delivery">
                  <Route path="persons" element={<div>配送员管理</div>} />
                  <Route path="areas" element={<div>配送区域</div>} />
                  <Route path="tasks" element={<div>配送任务</div>} />
                </Route>
                
                {/* 数据统计 */}
                <Route path="analytics">
                  <Route path="sales" element={<div>销售统计</div>} />
                  <Route path="products" element={<div>商品分析</div>} />
                  <Route path="customers" element={<div>客户分析</div>} />
                </Route>
                
                {/* 设置 */}
                <Route path="settings">
                  <Route path="merchant" element={<div>店铺设置</div>} />
                  <Route path="users" element={<div>用户管理</div>} />
                  <Route path="notifications" element={<div>通知设置</div>} />
                </Route>
                
                {/* 个人中心 */}
                <Route path="profile" element={<div>个人资料</div>} />
                <Route path="account-settings" element={<div>账户设置</div>} />
                <Route path="notifications" element={<div>通知中心</div>} />
              </Route>
              
              {/* 404 页面 */}
              <Route path="*" element={<Navigate to="/404" replace />} />
            </Routes>
          </div>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
};

export default App;
