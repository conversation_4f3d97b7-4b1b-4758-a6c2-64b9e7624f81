.main-layout {
  min-height: 100vh;
}

/* 侧边栏样式 */
.layout-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.layout-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Logo样式 */
.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.logo-icon {
  font-size: 24px;
  margin-right: 8px;
}

.logo-text {
  white-space: nowrap;
}

/* 菜单样式 */
.layout-menu {
  flex: 1;
  border-right: none;
  background: transparent;
}

.layout-menu .ant-menu-item,
.layout-menu .ant-menu-submenu-title {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

.layout-menu .ant-menu-item-selected {
  background: #1890ff !important;
  color: white;
}

.layout-menu .ant-menu-item:hover,
.layout-menu .ant-menu-submenu-title:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* 主内容区域 */
.site-layout {
  margin-left: 240px;
  transition: margin-left 0.2s;
}

.main-layout .ant-layout-sider-collapsed + .site-layout {
  margin-left: 80px;
}

/* 头部样式 */
.layout-header {
  background: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.trigger {
  font-size: 18px;
  color: #666;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.trigger:hover {
  background: #f5f5f5;
  color: #1890ff;
}

.merchant-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.merchant-status {
  font-size: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-btn {
  font-size: 18px;
  color: #666;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.notification-btn:hover {
  background: #f5f5f5;
  color: #1890ff;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background 0.2s;
}

.user-info:hover {
  background: #f5f5f5;
}

.username {
  font-weight: 500;
  color: #333;
}

/* 内容区域 */
.layout-content {
  margin: 0;
  padding: 0;
  min-height: calc(100vh - 64px);
  background: #f5f5f5;
}

.content-wrapper {
  padding: 24px;
  min-height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    position: fixed;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.2s;
  }
  
  .layout-sider.ant-layout-sider-collapsed {
    transform: translateX(-100%);
  }
  
  .site-layout {
    margin-left: 0;
  }
  
  .main-layout .ant-layout-sider-collapsed + .site-layout {
    margin-left: 0;
  }
  
  .layout-header {
    padding: 0 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .merchant-info {
    display: none;
  }
  
  .username {
    display: none;
  }
}

/* 滚动条样式 */
.layout-menu::-webkit-scrollbar {
  width: 4px;
}

.layout-menu::-webkit-scrollbar-track {
  background: transparent;
}

.layout-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.layout-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 菜单项图标对齐 */
.layout-menu .ant-menu-item .anticon,
.layout-menu .ant-menu-submenu-title .anticon {
  font-size: 16px;
  margin-right: 12px;
}

/* 子菜单样式 */
.layout-menu .ant-menu-submenu .ant-menu-item {
  padding-left: 48px !important;
  margin: 2px 8px;
  height: 36px;
  line-height: 36px;
}

.layout-menu .ant-menu-submenu-open .ant-menu-submenu-title {
  color: #1890ff;
}

/* 徽章样式 */
.ant-badge {
  display: flex;
  align-items: center;
}

/* 下拉菜单样式 */
.ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.ant-dropdown-menu-item {
  padding: 8px 16px;
}

.ant-dropdown-menu-item:hover {
  background: #f5f5f5;
}

/* 动画效果 */
.layout-sider,
.site-layout {
  transition: all 0.2s ease;
}

.trigger,
.notification-btn,
.user-info {
  transition: all 0.2s ease;
}
