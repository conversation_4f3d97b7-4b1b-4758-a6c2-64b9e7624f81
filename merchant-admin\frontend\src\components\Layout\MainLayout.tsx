import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Button, Typography } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  CustomerServiceOutlined,
  StarOutlined,
  CarOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  ProfileOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './MainLayout.css';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, merchant, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/products',
      icon: <ShopOutlined />,
      label: '菜品管理',
      children: [
        {
          key: '/products/categories',
          label: '分类管理',
        },
        {
          key: '/products/list',
          label: '菜品列表',
        },
        {
          key: '/products/add',
          label: '添加菜品',
        },
      ],
    },
    {
      key: '/orders',
      icon: <ShoppingCartOutlined />,
      label: '订单管理',
      children: [
        {
          key: '/orders/list',
          label: '订单列表',
        },
        {
          key: '/orders/pending',
          label: '待处理订单',
        },
        {
          key: '/orders/history',
          label: '历史订单',
        },
      ],
    },
    {
      key: '/refunds',
      icon: <CustomerServiceOutlined />,
      label: '售后管理',
      children: [
        {
          key: '/refunds/list',
          label: '退款申请',
        },
        {
          key: '/refunds/tickets',
          label: '客服工单',
        },
      ],
    },
    {
      key: '/reviews',
      icon: <StarOutlined />,
      label: '评价管理',
      children: [
        {
          key: '/reviews/list',
          label: '评价列表',
        },
        {
          key: '/reviews/reply',
          label: '待回复评价',
        },
      ],
    },
    {
      key: '/delivery',
      icon: <CarOutlined />,
      label: '配送管理',
      children: [
        {
          key: '/delivery/persons',
          label: '配送员管理',
        },
        {
          key: '/delivery/areas',
          label: '配送区域',
        },
        {
          key: '/delivery/tasks',
          label: '配送任务',
        },
      ],
    },
    {
      key: '/analytics',
      icon: <BarChartOutlined />,
      label: '数据统计',
      children: [
        {
          key: '/analytics/sales',
          label: '销售统计',
        },
        {
          key: '/analytics/products',
          label: '商品分析',
        },
        {
          key: '/analytics/customers',
          label: '客户分析',
        },
      ],
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '设置',
      children: [
        {
          key: '/settings/merchant',
          label: '店铺设置',
        },
        {
          key: '/settings/users',
          label: '用户管理',
        },
        {
          key: '/settings/notifications',
          label: '通知设置',
        },
      ],
    },
  ];

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
      onClick: () => navigate('/account-settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout className="main-layout">
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        className="layout-sider"
        width={240}
      >
        <div className="logo">
          <ShopOutlined className="logo-icon" />
          {!collapsed && <span className="logo-text">商家管理</span>}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="layout-menu"
        />
      </Sider>
      
      <Layout className="site-layout">
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="trigger"
            />
            
            <div className="merchant-info">
              <Text strong>{merchant?.name || '商家名称'}</Text>
              <Text type="secondary" className="merchant-status">
                {merchant?.status === 'approved' ? '营业中' : '未营业'}
              </Text>
            </div>
          </div>
          
          <div className="header-right">
            <Badge count={5} size="small">
              <Button 
                type="text" 
                icon={<BellOutlined />} 
                onClick={() => navigate('/notifications')}
                className="notification-btn"
              />
            </Badge>
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div className="user-info">
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />} 
                  src={user?.avatar}
                />
                <span className="username">{user?.username}</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        
        <Content className="layout-content">
          <div className="content-wrapper">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
