import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { message } from 'antd';
import { authService } from '../services/authService';
import { MerchantUser, Merchant } from '../types';

interface AuthContextType {
  user: MerchantUser | null;
  merchant: Merchant | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (credentials: { username: string; password: string; merchant_id?: number }) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<MerchantUser>) => void;
  checkPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<MerchantUser | null>(null);
  const [merchant, setMerchant] = useState<Merchant | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 检查本地存储的token和用户信息
    const token = localStorage.getItem('merchant_access_token');
    const userData = localStorage.getItem('merchant_user');
    const merchantData = localStorage.getItem('merchant_data');
    
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        const parsedMerchant = merchantData ? JSON.parse(merchantData) : null;
        
        setUser(parsedUser);
        setMerchant(parsedMerchant);
        
        // 验证token是否有效
        authService.getCurrentUser()
          .then(response => {
            if (response.success && response.data) {
              setUser(response.data);
              localStorage.setItem('merchant_user', JSON.stringify(response.data));
            } else {
              // token无效，清除本地存储
              clearAuthData();
            }
          })
          .catch(() => {
            // token无效，清除本地存储
            clearAuthData();
          })
          .finally(() => {
            setLoading(false);
          });
      } catch (error) {
        clearAuthData();
        setLoading(false);
      }
    } else {
      setLoading(false);
    }
  }, []);

  const clearAuthData = () => {
    localStorage.removeItem('merchant_access_token');
    localStorage.removeItem('merchant_refresh_token');
    localStorage.removeItem('merchant_user');
    localStorage.removeItem('merchant_data');
    setUser(null);
    setMerchant(null);
  };

  const login = async (credentials: { username: string; password: string; merchant_id?: number }): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await authService.login(credentials);
      
      if (response.success && response.data) {
        const { user: userData, access_token, refresh_token, merchant: merchantData } = response.data;
        
        // 存储token和用户信息
        localStorage.setItem('merchant_access_token', access_token);
        if (refresh_token) {
          localStorage.setItem('merchant_refresh_token', refresh_token);
        }
        localStorage.setItem('merchant_user', JSON.stringify(userData));
        if (merchantData) {
          localStorage.setItem('merchant_data', JSON.stringify(merchantData));
          setMerchant(merchantData);
        }
        
        setUser(userData);
        message.success(response.message || '登录成功');
        return true;
      } else {
        message.error(response.message || '登录失败');
        return false;
      }
    } catch (error) {
      message.error('登录失败，请稍后重试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      // 忽略退出登录的错误
    } finally {
      clearAuthData();
      message.success('已退出登录');
    }
  };

  const updateUser = (userData: Partial<MerchantUser>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('merchant_user', JSON.stringify(updatedUser));
    }
  };

  const checkPermission = (permission: string): boolean => {
    if (!user || !user.permissions) {
      return false;
    }
    
    // 如果有 'all' 权限，则拥有所有权限
    if (user.permissions.includes('all')) {
      return true;
    }
    
    return user.permissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    if (!user) {
      return false;
    }
    
    return user.role === role;
  };

  const isAuthenticated = !!user && !!localStorage.getItem('merchant_access_token');

  const value: AuthContextType = {
    user,
    merchant,
    loading,
    isAuthenticated,
    login,
    logout,
    updateUser,
    checkPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
