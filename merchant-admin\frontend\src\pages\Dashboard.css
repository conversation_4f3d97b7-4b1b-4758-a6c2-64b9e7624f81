.dashboard {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h2 {
  margin-bottom: 4px;
  color: #262626;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-row .ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stats-row .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.stats-row .ant-statistic {
  text-align: center;
}

.stats-row .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.stats-row .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.stats-row .ant-statistic-content-prefix {
  font-size: 20px;
  margin-right: 8px;
}

.content-row {
  margin-bottom: 24px;
}

.content-row .ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 100%;
}

.chart-card .ant-card-body {
  padding: 20px;
}

.stock-alert-card .ant-card-head-title {
  color: #fa8c16;
  font-weight: 600;
}

.stock-alert-card .ant-list-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stock-alert-card .ant-list-item:last-child {
  border-bottom: none;
}

.stock-alert-card .ant-list-item-meta-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.stock-alert-card .ant-progress {
  margin-top: 4px;
}

.recent-orders-card .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

.recent-orders-card .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.top-products-card .ant-card-head-title {
  color: #fa8c16;
  font-weight: 600;
}

.top-products-card .ant-list-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.top-products-card .ant-list-item:last-child {
  border-bottom: none;
}

.top-products-card .ant-list-item-meta-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.top-products-card .ant-list-item-meta-description {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 0;
  }
  
  .dashboard-header {
    margin-bottom: 16px;
  }
  
  .stats-row {
    margin-bottom: 16px;
  }
  
  .content-row {
    margin-bottom: 16px;
  }
  
  .stats-row .ant-statistic-content {
    font-size: 20px;
  }
  
  .chart-card .ant-card-body {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .stats-row .ant-statistic-content {
    font-size: 18px;
  }
  
  .stats-row .ant-statistic-content-prefix {
    font-size: 16px;
  }
  
  .dashboard-header h2 {
    font-size: 20px;
  }
}

/* 卡片标题图标样式 */
.ant-card-head-title .anticon {
  margin-right: 8px;
  font-size: 16px;
}

/* 统计数字后缀样式 */
.ant-statistic-content-suffix {
  font-size: 12px;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 表格复制按钮样式 */
.ant-typography-copy {
  margin-left: 8px;
  color: #1890ff;
}

/* 进度条样式 */
.ant-progress-line {
  margin: 0;
}

.ant-progress-bg {
  border-radius: 2px;
}

/* 列表头像样式 */
.ant-list-item-meta-avatar .ant-avatar {
  font-weight: 600;
  font-size: 12px;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 图表容器样式 */
.chart-card .echarts-for-react {
  width: 100% !important;
}

/* 卡片悬停效果 */
.ant-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 文本颜色 */
.ant-typography {
  color: #262626;
}

.ant-typography.ant-typography-secondary {
  color: #8c8c8c;
}

.ant-typography.ant-typography-success {
  color: #52c41a;
}

.ant-typography.ant-typography-danger {
  color: #ff4d4f;
}

/* 加载状态 */
.ant-spin-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 空状态 */
.ant-empty {
  padding: 40px 0;
}

/* 表格行高 */
.recent-orders-card .ant-table-tbody > tr > td {
  padding: 12px 16px;
}

/* 列表项间距 */
.ant-list-item-meta {
  align-items: flex-start;
}

.ant-list-item-meta-content {
  flex: 1;
  min-width: 0;
}
