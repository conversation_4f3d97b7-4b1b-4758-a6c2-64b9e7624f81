import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Tag, Progress, List, Avatar } from 'antd';
import {
  ShoppingCartOutlined,
  DollarOutlined,
  UserOutlined,
  StarOutlined,
  TrophyOutlined,
  AlertOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { useAuth } from '../contexts/AuthContext';
import './Dashboard.css';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { merchant } = useAuth();
  const [loading, setLoading] = useState(false);

  // 模拟数据
  const [dashboardData] = useState({
    stats: {
      todayOrders: 156,
      todayRevenue: 8650.50,
      monthOrders: 3420,
      monthRevenue: 125680.00,
      totalProducts: 89,
      lowStockProducts: 5,
      averageRating: 4.8,
      totalReviews: 1250
    },
    recentOrders: [
      {
        id: 1,
        orderNumber: 'ORD202412120001',
        customerName: '张三',
        amount: 68.50,
        status: 'pending',
        time: '2024-12-12 14:30'
      },
      {
        id: 2,
        orderNumber: 'ORD202412120002',
        customerName: '李四',
        amount: 125.00,
        status: 'confirmed',
        time: '2024-12-12 14:25'
      },
      {
        id: 3,
        orderNumber: 'ORD202412120003',
        customerName: '王五',
        amount: 89.00,
        status: 'preparing',
        time: '2024-12-12 14:20'
      }
    ],
    topProducts: [
      { name: '宫保鸡丁', sales: 89, revenue: 2670 },
      { name: '麻婆豆腐', sales: 76, revenue: 2280 },
      { name: '红烧肉', sales: 65, revenue: 2600 },
      { name: '糖醋里脊', sales: 58, revenue: 2030 },
      { name: '鱼香肉丝', sales: 52, revenue: 1820 }
    ],
    lowStockProducts: [
      { name: '宫保鸡丁', stock: 5, minStock: 10 },
      { name: '麻婆豆腐', stock: 3, minStock: 8 },
      { name: '红烧肉', stock: 2, minStock: 5 }
    ]
  });

  // 销售趋势图表配置
  const salesChartOption = {
    title: {
      text: '近7天销售趋势',
      textStyle: { fontSize: 14, fontWeight: 'normal' }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['订单数', '销售额']
    },
    xAxis: {
      type: 'category',
      data: ['12-06', '12-07', '12-08', '12-09', '12-10', '12-11', '12-12']
    },
    yAxis: [
      {
        type: 'value',
        name: '订单数',
        position: 'left'
      },
      {
        type: 'value',
        name: '销售额(元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '订单数',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230, 156],
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '销售额',
        type: 'bar',
        yAxisIndex: 1,
        data: [6200, 7100, 5800, 7300, 5200, 12500, 8650],
        itemStyle: { color: '#52c41a' }
      }
    ]
  };

  // 订单状态配置
  const orderStatusConfig = {
    pending: { color: 'orange', text: '待确认' },
    confirmed: { color: 'blue', text: '已确认' },
    preparing: { color: 'purple', text: '制作中' },
    delivering: { color: 'cyan', text: '配送中' },
    completed: { color: 'green', text: '已完成' },
    cancelled: { color: 'red', text: '已取消' }
  };

  // 最近订单表格列配置
  const orderColumns = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text: string) => <Text copyable={{ text }}>{text}</Text>
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName'
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `¥${amount.toFixed(2)}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const config = orderStatusConfig[status as keyof typeof orderStatusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time'
    }
  ];

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <Title level={2}>仪表盘</Title>
        <Text type="secondary">欢迎回来，{merchant?.name || '商家'}！</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日订单"
              value={dashboardData.stats.todayOrders}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <span style={{ fontSize: '14px', color: '#52c41a' }}>
                  <RiseOutlined /> +12%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日营收"
              value={dashboardData.stats.todayRevenue}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#52c41a' }}
              suffix={
                <span style={{ fontSize: '14px', color: '#52c41a' }}>
                  <RiseOutlined /> +8%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="本月订单"
              value={dashboardData.stats.monthOrders}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
              suffix={
                <span style={{ fontSize: '14px', color: '#f5222d' }}>
                  <FallOutlined /> -3%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均评分"
              value={dashboardData.stats.averageRating}
              prefix={<StarOutlined />}
              precision={1}
              valueStyle={{ color: '#fa8c16' }}
              suffix={`/ 5.0 (${dashboardData.stats.totalReviews})`}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} className="content-row">
        {/* 销售趋势图 */}
        <Col xs={24} lg={16}>
          <Card title="销售趋势" className="chart-card">
            <ReactECharts 
              option={salesChartOption} 
              style={{ height: '300px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>

        {/* 库存预警 */}
        <Col xs={24} lg={8}>
          <Card 
            title={
              <span>
                <AlertOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
                库存预警
              </span>
            }
            className="stock-alert-card"
          >
            <List
              dataSource={dashboardData.lowStockProducts}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={item.name}
                    description={
                      <div>
                        <Text type="danger">库存: {item.stock}</Text>
                        <br />
                        <Progress 
                          percent={(item.stock / item.minStock) * 100} 
                          size="small"
                          status={item.stock < item.minStock ? 'exception' : 'normal'}
                          showInfo={false}
                        />
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} className="content-row">
        {/* 最近订单 */}
        <Col xs={24} lg={14}>
          <Card title="最近订单" className="recent-orders-card">
            <Table
              dataSource={dashboardData.recentOrders}
              columns={orderColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>

        {/* 热销商品 */}
        <Col xs={24} lg={10}>
          <Card 
            title={
              <span>
                <TrophyOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
                热销商品
              </span>
            }
            className="top-products-card"
          >
            <List
              dataSource={dashboardData.topProducts}
              renderItem={(item, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ 
                          backgroundColor: index < 3 ? '#fa8c16' : '#d9d9d9',
                          color: 'white'
                        }}
                      >
                        {index + 1}
                      </Avatar>
                    }
                    title={item.name}
                    description={
                      <div>
                        <Text>销量: {item.sales}</Text>
                        <br />
                        <Text type="success">营收: ¥{item.revenue}</Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
