.forgot-password-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.forgot-password-content {
  width: 100%;
  max-width: 600px;
}

.forgot-password-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: none;
  overflow: hidden;
}

.forgot-header {
  margin-bottom: 24px;
}

.back-button {
  color: #1890ff;
  font-weight: 500;
  padding: 0;
  height: auto;
}

.back-button:hover {
  color: #40a9ff;
}

.forgot-steps {
  margin-bottom: 32px;
}

.forgot-steps .ant-steps-item-title {
  font-size: 14px;
  font-weight: 500;
}

.forgot-steps .ant-steps-item-description {
  font-size: 12px;
  color: #8c8c8c;
}

.forgot-content {
  min-height: 400px;
}

.forgot-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.step-content {
  flex: 1;
  text-align: center;
  padding: 20px 0;
}

.step-content h4 {
  color: #262626;
  margin-bottom: 12px;
}

.step-content .ant-typography {
  margin-bottom: 0;
  line-height: 1.6;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.step-actions .ant-btn {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
}

.resend-section {
  margin-top: 16px;
  text-align: center;
}

.resend-section .ant-btn-link {
  padding: 0;
  height: auto;
  margin-left: 8px;
}

.success-content {
  padding: 20px 0;
}

.success-content .ant-result {
  padding: 0;
}

.success-content .ant-result-title {
  color: #262626;
  font-size: 20px;
  font-weight: 600;
}

.success-content .ant-result-subtitle {
  color: #595959;
  font-size: 14px;
  margin-top: 8px;
}

/* 表单样式 */
.ant-form-item {
  margin-bottom: 20px;
  text-align: left;
}

.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-affix-wrapper {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-input:hover,
.ant-input-affix-wrapper:hover {
  border-color: #40a9ff;
}

/* 按钮样式 */
.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 步骤条样式 */
.ant-steps-item-process .ant-steps-item-icon {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-steps-item-finish .ant-steps-item-icon {
  background: #52c41a;
  border-color: #52c41a;
}

.ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
  color: white;
}

/* 输入框前缀图标 */
.ant-input-prefix {
  color: #bfbfbf;
  margin-right: 8px;
}

.ant-input-affix-wrapper-focused .ant-input-prefix {
  color: #1890ff;
}

/* 链接样式 */
.ant-btn-link {
  color: #1890ff;
  text-decoration: none;
}

.ant-btn-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forgot-password-container {
    padding: 16px;
  }
  
  .forgot-password-content {
    max-width: 100%;
  }
  
  .forgot-password-card {
    margin: 0;
  }
  
  .forgot-steps {
    margin-bottom: 24px;
  }
  
  .step-content {
    padding: 16px 0;
  }
  
  .step-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .step-actions .ant-btn {
    width: 100%;
    max-width: 200px;
  }
  
  .forgot-steps .ant-steps-item-title {
    font-size: 12px;
  }
  
  .forgot-steps .ant-steps-item-description {
    display: none;
  }
  
  .forgot-content {
    min-height: 350px;
  }
}

@media (max-width: 576px) {
  .step-content h4 {
    font-size: 18px;
  }
  
  .success-content .ant-result-title {
    font-size: 18px;
  }
  
  .success-content .ant-result-subtitle {
    font-size: 13px;
  }
  
  .step-content {
    padding: 12px 0;
  }
  
  .forgot-content {
    min-height: 300px;
  }
}

/* 加载状态 */
.ant-btn-loading {
  pointer-events: none;
}

/* 错误提示样式 */
.ant-form-item-explain-error {
  font-size: 12px;
  margin-top: 4px;
}

/* 内容动画 */
.step-content,
.success-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证状态 */
.ant-form-item-has-success .ant-input,
.ant-form-item-has-success .ant-input-affix-wrapper {
  border-color: #52c41a;
}

.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-affix-wrapper {
  border-color: #ff4d4f;
}

/* 结果页面样式 */
.ant-result-success .ant-result-icon {
  color: #52c41a;
}

.ant-result-extra {
  margin-top: 24px;
}

.ant-result-extra .ant-btn {
  margin: 0 8px;
}

/* 文本选择样式 */
::selection {
  background: #bae7ff;
  color: #262626;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 强调文本样式 */
.ant-typography strong {
  color: #1890ff;
  font-weight: 600;
}

/* 禁用状态 */
.ant-input:disabled {
  background-color: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

/* 卡片悬停效果 */
.forgot-password-card {
  transition: all 0.3s ease;
}

/* 按钮悬停效果 */
.ant-btn {
  transition: all 0.3s ease;
}

.ant-btn:hover {
  transform: translateY(-1px);
}
