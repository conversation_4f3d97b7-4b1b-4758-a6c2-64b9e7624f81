import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, Steps, message, Result } from 'antd';
import { MailOutlined, SafetyOutlined, LockOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';
import './ForgotPasswordPage.css';

const { Title, Text } = Typography;
const { Step } = Steps;

const ForgotPasswordPage: React.FC = () => {
  const [emailForm] = Form.useForm();
  const [resetForm] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const navigate = useNavigate();

  const steps = [
    {
      title: '输入邮箱',
      description: '输入注册时使用的邮箱',
    },
    {
      title: '验证邮箱',
      description: '查收邮件并输入验证码',
    },
    {
      title: '重置密码',
      description: '设置新的登录密码',
    },
    {
      title: '完成重置',
      description: '密码重置成功',
    },
  ];

  // 发送重置邮件
  const handleSendEmail = async (values: { email: string }) => {
    try {
      setLoading(true);
      const response = await authService.forgotPassword(values.email);
      
      if (response.success) {
        setEmail(values.email);
        setCurrentStep(1);
        message.success('重置邮件已发送，请查收');
      } else {
        message.error(response.message || '发送失败，请重试');
      }
    } catch (error) {
      message.error('发送失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 验证重置码并重置密码
  const handleResetPassword = async (values: { code: string; password: string }) => {
    try {
      setLoading(true);
      const response = await authService.resetPassword(values.code, values.password);
      
      if (response.success) {
        setCurrentStep(3);
        message.success('密码重置成功');
      } else {
        message.error(response.message || '重置失败，请重试');
      }
    } catch (error) {
      message.error('重置失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 重新发送邮件
  const handleResendEmail = async () => {
    try {
      setLoading(true);
      const response = await authService.forgotPassword(email);
      
      if (response.success) {
        message.success('重置邮件已重新发送');
      } else {
        message.error(response.message || '发送失败，请重试');
      }
    } catch (error) {
      message.error('发送失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 渲染邮箱输入步骤
  const renderEmailStep = () => (
    <Form
      form={emailForm}
      onFinish={handleSendEmail}
      layout="vertical"
      className="forgot-form"
    >
      <div className="step-content">
        <Title level={4}>找回密码</Title>
        <Text type="secondary">
          请输入您注册时使用的邮箱地址，我们将向该邮箱发送密码重置链接
        </Text>
        
        <Form.Item
          name="email"
          label="邮箱地址"
          rules={[
            { required: true, message: '请输入邮箱地址' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]}
          style={{ marginTop: 24 }}
        >
          <Input
            prefix={<MailOutlined />}
            placeholder="请输入注册邮箱"
            size="large"
          />
        </Form.Item>
      </div>

      <div className="step-actions">
        <Button onClick={() => navigate('/login')}>
          返回登录
        </Button>
        <Button type="primary" htmlType="submit" loading={loading}>
          发送重置邮件
        </Button>
      </div>
    </Form>
  );

  // 渲染验证码和密码重置步骤
  const renderResetStep = () => (
    <Form
      form={resetForm}
      onFinish={handleResetPassword}
      layout="vertical"
      className="forgot-form"
    >
      <div className="step-content">
        <Title level={4}>重置密码</Title>
        <Text type="secondary">
          我们已向 <Text strong>{email}</Text> 发送了重置邮件，请查收邮件并输入验证码
        </Text>
        
        <Form.Item
          name="code"
          label="验证码"
          rules={[
            { required: true, message: '请输入验证码' },
            { len: 6, message: '验证码为6位' }
          ]}
          style={{ marginTop: 24 }}
        >
          <Input
            prefix={<SafetyOutlined />}
            placeholder="请输入6位验证码"
            size="large"
            maxLength={6}
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="新密码"
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 6, max: 20, message: '密码长度为6-20个字符' },
            { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字' }
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入新密码"
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="确认密码"
          dependencies={['password']}
          rules={[
            { required: true, message: '请确认新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请再次输入新密码"
            size="large"
          />
        </Form.Item>

        <div className="resend-section">
          <Text type="secondary">没有收到邮件？</Text>
          <Button type="link" onClick={handleResendEmail} loading={loading}>
            重新发送
          </Button>
        </div>
      </div>

      <div className="step-actions">
        <Button onClick={() => setCurrentStep(0)}>
          重新输入邮箱
        </Button>
        <Button type="primary" htmlType="submit" loading={loading}>
          重置密码
        </Button>
      </div>
    </Form>
  );

  // 渲染完成步骤
  const renderSuccessStep = () => (
    <div className="success-content">
      <Result
        status="success"
        title="密码重置成功！"
        subTitle="您的密码已成功重置，现在可以使用新密码登录了"
        extra={[
          <Button type="primary" key="login" onClick={() => navigate('/login')}>
            立即登录
          </Button>,
          <Button key="home" onClick={() => navigate('/')}>
            返回首页
          </Button>
        ]}
      />
    </div>
  );

  return (
    <div className="forgot-password-container">
      <div className="forgot-password-content">
        <Card className="forgot-password-card">
          <div className="forgot-header">
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/login')}
              className="back-button"
            >
              返回登录
            </Button>
          </div>

          <Steps current={currentStep} className="forgot-steps">
            {steps.map(item => (
              <Step key={item.title} title={item.title} description={item.description} />
            ))}
          </Steps>

          <div className="forgot-content">
            {currentStep === 0 && renderEmailStep()}
            {(currentStep === 1 || currentStep === 2) && renderResetStep()}
            {currentStep === 3 && renderSuccessStep()}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
