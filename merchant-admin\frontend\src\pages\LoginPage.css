.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-content {
  width: 100%;
  max-width: 400px;
}

.login-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: none;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  color: #1890ff;
  margin-bottom: 8px;
}

.code-input-group {
  display: flex;
  gap: 8px;
}

.code-input-group .ant-input {
  flex: 1;
}

.send-code-btn {
  white-space: nowrap;
  min-width: 100px;
}

.login-footer {
  text-align: center;
  margin-top: 16px;
}

.login-footer .ant-btn-link {
  padding: 0;
  height: auto;
}

.demo-info {
  text-align: center;
  margin-top: 24px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }
  
  .login-content {
    max-width: 100%;
  }
  
  .login-card {
    margin: 0;
  }
}

/* 表单样式优化 */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-input-affix-wrapper,
.ant-input {
  border-radius: 8px;
}

.ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 标签页样式 */
.ant-tabs-tab {
  font-weight: 500;
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1890ff;
  font-weight: 600;
}

/* 输入框图标颜色 */
.ant-input-prefix {
  color: #bfbfbf;
}

.ant-input:focus .ant-input-prefix,
.ant-input-affix-wrapper-focused .ant-input-prefix {
  color: #1890ff;
}

/* 加载状态 */
.ant-btn-loading {
  pointer-events: none;
}

/* 验证码按钮禁用状态 */
.send-code-btn:disabled {
  color: #bfbfbf;
  background: #f5f5f5;
  border-color: #d9d9d9;
}

/* 错误提示样式 */
.ant-form-item-explain-error {
  font-size: 12px;
}

/* 分割线样式 */
.ant-divider {
  margin: 16px 0;
  border-color: #f0f0f0;
}

/* 链接按钮样式 */
.ant-btn-link {
  color: #1890ff;
  text-decoration: none;
}

.ant-btn-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}
