import React, { useState } from 'react';
import { Form, Input, But<PERSON>, Card, Typography, message, Tabs, Divider } from 'antd';
import { UserOutlined, LockOutlined, PhoneOutlined, SafetyOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/authService';
import './LoginPage.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const LoginPage: React.FC = () => {
  const [passwordForm] = Form.useForm();
  const [codeForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [codeLoading, setSendCodeLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/dashboard';

  // 密码登录
  const handlePasswordLogin = async (values: any) => {
    try {
      setLoading(true);
      const success = await login({
        username: values.username,
        password: values.password
      });
      
      if (success) {
        navigate(from, { replace: true });
      }
    } catch (error) {
      message.error('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 验证码登录
  const handleCodeLogin = async (values: any) => {
    try {
      setLoading(true);
      const response = await authService.loginWithCode(values.phone, values.code);
      
      if (response.success) {
        // 处理登录成功逻辑
        const { user, access_token, refresh_token, merchant } = response.data;
        
        localStorage.setItem('merchant_access_token', access_token);
        if (refresh_token) {
          localStorage.setItem('merchant_refresh_token', refresh_token);
        }
        localStorage.setItem('merchant_user', JSON.stringify(user));
        if (merchant) {
          localStorage.setItem('merchant_data', JSON.stringify(merchant));
        }
        
        message.success(response.message);
        navigate(from, { replace: true });
        window.location.reload(); // 刷新页面以更新认证状态
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 发送验证码
  const handleSendCode = async () => {
    try {
      const phone = codeForm.getFieldValue('phone');
      if (!phone) {
        message.error('请输入手机号');
        return;
      }

      setSendCodeLoading(true);
      const response = await authService.sendVerificationCode(phone);
      
      if (response.success) {
        message.success('验证码已发送');
        setCountdown(60);
        
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('发送验证码失败');
    } finally {
      setSendCodeLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-content">
        <Card className="login-card">
          <div className="login-header">
            <Title level={2}>商家管理系统</Title>
            <Text type="secondary">欢迎登录您的商家管理后台</Text>
          </div>

          <Tabs defaultActiveKey="password" centered>
            <TabPane tab="密码登录" key="password">
              <Form
                form={passwordForm}
                name="password_login"
                onFinish={handlePasswordLogin}
                autoComplete="off"
                layout="vertical"
              >
                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 3, message: '用户名长度不能少于3位' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="请输入用户名"
                    size="large"
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, message: '密码长度不能少于6位' }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="请输入密码"
                    size="large"
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    size="large"
                    loading={loading}
                    block
                  >
                    登录
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>

            <TabPane tab="验证码登录" key="code">
              <Form
                form={codeForm}
                name="code_login"
                onFinish={handleCodeLogin}
                autoComplete="off"
                layout="vertical"
              >
                <Form.Item
                  name="phone"
                  rules={[
                    { required: true, message: '请输入手机号' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                  ]}
                >
                  <Input
                    prefix={<PhoneOutlined />}
                    placeholder="请输入手机号"
                    size="large"
                  />
                </Form.Item>

                <Form.Item
                  name="code"
                  rules={[
                    { required: true, message: '请输入验证码' },
                    { len: 6, message: '验证码为6位数字' }
                  ]}
                >
                  <div className="code-input-group">
                    <Input
                      prefix={<SafetyOutlined />}
                      placeholder="请输入验证码"
                      size="large"
                      maxLength={6}
                    />
                    <Button
                      type="default"
                      onClick={handleSendCode}
                      loading={codeLoading}
                      disabled={countdown > 0}
                      className="send-code-btn"
                    >
                      {countdown > 0 ? `${countdown}s` : '发送验证码'}
                    </Button>
                  </div>
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    size="large"
                    loading={loading}
                    block
                  >
                    登录
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>
          </Tabs>

          <Divider />

          <div className="login-footer">
            <Text type="secondary">
              还没有账户？
              <Button type="link" onClick={() => navigate('/register')}>
                立即注册
              </Button>
            </Text>
            <br />
            <Text type="secondary">
              忘记密码？
              <Button type="link" onClick={() => navigate('/forgot-password')}>
                找回密码
              </Button>
            </Text>
          </div>

          <div className="demo-info">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              演示账户：admin / 123456
            </Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;
