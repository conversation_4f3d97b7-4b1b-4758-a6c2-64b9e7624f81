.merchant-status-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.merchant-status-content {
  width: 100%;
  max-width: 800px;
}

.merchant-status-loading {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: none;
  overflow: hidden;
}

.status-card .ant-result {
  padding: 40px 24px 24px;
}

.status-card .ant-result-title {
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.status-card .ant-result-subtitle {
  color: #595959;
  font-size: 16px;
  margin-top: 8px;
}

.status-card .ant-result-icon {
  font-size: 72px;
  margin-bottom: 24px;
}

.status-details {
  margin: 32px 0;
  padding: 24px;
  background: #fafafa;
  border-radius: 8px;
}

.status-details h4 {
  color: #262626;
  margin-bottom: 20px;
  text-align: center;
}

.status-details .ant-descriptions {
  background: white;
  border-radius: 6px;
}

.status-details .ant-descriptions-item-label {
  font-weight: 600;
  color: #262626;
  background: #f5f5f5;
}

.status-details .ant-descriptions-item-content {
  color: #595959;
}

.status-alert {
  margin: 24px 0;
  border-radius: 8px;
}

.status-alert .ant-alert-message {
  font-weight: 600;
  color: #262626;
}

.status-alert .ant-alert-description {
  margin-top: 8px;
}

.status-alert ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.status-alert li {
  margin-bottom: 4px;
  color: #595959;
}

.contact-section {
  margin: 32px 0;
  padding: 24px;
  background: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.contact-section h4 {
  color: #262626;
  margin-bottom: 20px;
}

.contact-methods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.contact-icon {
  font-size: 20px;
  color: #1890ff;
  margin-top: 4px;
}

.contact-item strong {
  color: #262626;
  font-size: 14px;
}

.contact-item .ant-typography {
  font-size: 13px;
  margin-top: 2px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  flex-wrap: wrap;
}

.action-buttons .ant-btn {
  min-width: 100px;
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
}

.action-buttons .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.action-buttons .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 状态标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  font-weight: 500;
}

/* 描述列表样式 */
.ant-descriptions-bordered .ant-descriptions-item-label {
  width: 120px;
}

.ant-descriptions-bordered .ant-descriptions-item-content {
  word-break: break-word;
}

/* 警告框图标 */
.ant-alert-icon {
  font-size: 16px;
}

/* 复制按钮样式 */
.ant-typography-copy {
  margin-left: 8px;
  color: #1890ff;
}

.ant-typography-copy:hover {
  color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .merchant-status-container {
    padding: 16px;
  }
  
  .merchant-status-content {
    max-width: 100%;
  }
  
  .status-card .ant-result {
    padding: 32px 16px 16px;
  }
  
  .status-card .ant-result-title {
    font-size: 20px;
  }
  
  .status-card .ant-result-subtitle {
    font-size: 14px;
  }
  
  .status-card .ant-result-icon {
    font-size: 60px;
    margin-bottom: 20px;
  }
  
  .status-details,
  .contact-section {
    margin: 24px 0;
    padding: 20px;
  }
  
  .contact-methods {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  .action-buttons .ant-btn {
    width: 100%;
    max-width: 200px;
  }
  
  .ant-descriptions-bordered .ant-descriptions-item-label {
    width: 80px;
    font-size: 12px;
  }
  
  .ant-descriptions-bordered .ant-descriptions-item-content {
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .status-card .ant-result-title {
    font-size: 18px;
  }
  
  .status-card .ant-result-subtitle {
    font-size: 13px;
  }
  
  .status-details,
  .contact-section {
    padding: 16px;
  }
  
  .contact-item {
    gap: 8px;
  }
  
  .contact-icon {
    font-size: 18px;
  }
  
  .action-buttons .ant-btn {
    height: 40px;
    font-size: 14px;
  }
}

/* 加载动画 */
.merchant-status-loading .ant-spin {
  color: white;
}

.merchant-status-loading .ant-spin-text {
  color: white;
}

/* 卡片悬停效果 */
.status-card {
  transition: all 0.3s ease;
}

/* 按钮悬停效果 */
.ant-btn {
  transition: all 0.3s ease;
}

.ant-btn:hover {
  transform: translateY(-1px);
}

/* 文本选择样式 */
::selection {
  background: #bae7ff;
  color: #262626;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 状态特定样式 */
.status-card .ant-result-success .ant-result-icon {
  color: #52c41a;
}

.status-card .ant-result-error .ant-result-icon {
  color: #ff4d4f;
}

.status-card .ant-result-warning .ant-result-icon {
  color: #fa8c16;
}

.status-card .ant-result-info .ant-result-icon {
  color: #1890ff;
}

/* 危险文本样式 */
.ant-typography-danger {
  color: #ff4d4f;
  font-weight: 500;
}
