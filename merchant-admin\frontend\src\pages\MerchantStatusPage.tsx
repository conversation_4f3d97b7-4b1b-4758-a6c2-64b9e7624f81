import React, { useState, useEffect } from 'react';
import { Result, But<PERSON>, Card, Typography, Alert, Descriptions, Tag, Spin } from 'antd';
import { 
  ClockCircleOutlined, 
  ExclamationCircleOutlined, 
  CloseCircleOutlined,
  CheckCircleOutlined,
  PhoneOutlined,
  MailOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/authService';
import './MerchantStatusPage.css';

const { Title, Text, Paragraph } = Typography;

interface MerchantStatus {
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  reason?: string;
  submitted_at: string;
  reviewed_at?: string;
  reviewer_note?: string;
  next_steps?: string[];
}

const MerchantStatusPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [merchantStatus, setMerchantStatus] = useState<MerchantStatus | null>(null);
  const { merchant, logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    fetchMerchantStatus();
  }, []);

  const fetchMerchantStatus = async () => {
    try {
      setLoading(true);
      const response = await authService.checkMerchantStatus();
      
      if (response.success) {
        setMerchantStatus(response.data);
      } else {
        // 模拟数据
        setMerchantStatus({
          status: merchant?.status as any || 'pending',
          submitted_at: '2024-12-12 10:30:00',
          reason: merchant?.status === 'rejected' ? '营业执照信息不清晰，请重新上传' : undefined,
          reviewer_note: merchant?.status === 'rejected' ? '请提供清晰的营业执照照片，确保所有信息可见' : undefined,
          next_steps: merchant?.status === 'rejected' ? [
            '重新上传清晰的营业执照照片',
            '确保联系信息准确无误',
            '等待重新审核'
          ] : [
            '等待工作人员审核',
            '保持联系方式畅通',
            '准备开店资料'
          ]
        });
      }
    } catch (error) {
      console.error('获取商家状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          status: 'info' as const,
          title: '审核中',
          subTitle: '您的商家入驻申请正在审核中，请耐心等待',
          icon: <ClockCircleOutlined style={{ color: '#1890ff' }} />,
          color: '#1890ff'
        };
      case 'approved':
        return {
          status: 'success' as const,
          title: '审核通过',
          subTitle: '恭喜！您的商家入驻申请已通过审核',
          icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
          color: '#52c41a'
        };
      case 'rejected':
        return {
          status: 'error' as const,
          title: '审核未通过',
          subTitle: '很抱歉，您的商家入驻申请未通过审核',
          icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
          color: '#ff4d4f'
        };
      case 'suspended':
        return {
          status: 'warning' as const,
          title: '账户暂停',
          subTitle: '您的商家账户已被暂停，请联系客服处理',
          icon: <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />,
          color: '#fa8c16'
        };
      default:
        return {
          status: 'info' as const,
          title: '状态未知',
          subTitle: '无法获取当前状态，请联系客服',
          icon: <ExclamationCircleOutlined style={{ color: '#8c8c8c' }} />,
          color: '#8c8c8c'
        };
    }
  };

  const getStatusTag = (status: string) => {
    const config = getStatusConfig(status);
    return <Tag color={config.color}>{config.title}</Tag>;
  };

  const handleResubmit = () => {
    navigate('/register');
  };

  const handleContactService = () => {
    // 可以打开客服聊天窗口或跳转到联系页面
    window.open('/contact');
  };

  if (loading) {
    return (
      <div className="merchant-status-loading">
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (!merchantStatus) {
    return (
      <div className="merchant-status-container">
        <Result
          status="error"
          title="无法获取状态信息"
          subTitle="请稍后重试或联系客服"
          extra={[
            <Button type="primary" onClick={fetchMerchantStatus}>
              重新加载
            </Button>,
            <Button onClick={() => navigate('/contact')}>
              联系客服
            </Button>
          ]}
        />
      </div>
    );
  }

  const statusConfig = getStatusConfig(merchantStatus.status);

  return (
    <div className="merchant-status-container">
      <div className="merchant-status-content">
        <Card className="status-card">
          <Result
            status={statusConfig.status}
            title={statusConfig.title}
            subTitle={statusConfig.subTitle}
            icon={statusConfig.icon}
          />

          <div className="status-details">
            <Title level={4}>申请详情</Title>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="商家名称">
                {merchant?.name || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="申请状态">
                {getStatusTag(merchantStatus.status)}
              </Descriptions.Item>
              <Descriptions.Item label="提交时间">
                {merchantStatus.submitted_at}
              </Descriptions.Item>
              {merchantStatus.reviewed_at && (
                <Descriptions.Item label="审核时间">
                  {merchantStatus.reviewed_at}
                </Descriptions.Item>
              )}
              {merchantStatus.reason && (
                <Descriptions.Item label="原因">
                  <Text type="danger">{merchantStatus.reason}</Text>
                </Descriptions.Item>
              )}
              {merchantStatus.reviewer_note && (
                <Descriptions.Item label="审核备注">
                  {merchantStatus.reviewer_note}
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>

          {merchantStatus.status === 'rejected' && (
            <Alert
              message="审核未通过"
              description={
                <div>
                  <Paragraph>
                    您的申请未通过审核，请根据以下建议修改后重新提交：
                  </Paragraph>
                  {merchantStatus.next_steps && (
                    <ul>
                      {merchantStatus.next_steps.map((step, index) => (
                        <li key={index}>{step}</li>
                      ))}
                    </ul>
                  )}
                </div>
              }
              type="error"
              showIcon
              className="status-alert"
            />
          )}

          {merchantStatus.status === 'pending' && (
            <Alert
              message="审核进行中"
              description={
                <div>
                  <Paragraph>
                    我们正在审核您的申请，通常需要1-3个工作日。请确保：
                  </Paragraph>
                  {merchantStatus.next_steps && (
                    <ul>
                      {merchantStatus.next_steps.map((step, index) => (
                        <li key={index}>{step}</li>
                      ))}
                    </ul>
                  )}
                </div>
              }
              type="info"
              showIcon
              className="status-alert"
            />
          )}

          {merchantStatus.status === 'suspended' && (
            <Alert
              message="账户已暂停"
              description="您的账户因违反平台规则被暂停使用，请联系客服了解详情并申请恢复。"
              type="warning"
              showIcon
              className="status-alert"
            />
          )}

          <div className="contact-section">
            <Title level={4}>需要帮助？</Title>
            <div className="contact-methods">
              <div className="contact-item">
                <PhoneOutlined className="contact-icon" />
                <div>
                  <Text strong>客服热线</Text>
                  <br />
                  <Text copyable>************</Text>
                  <br />
                  <Text type="secondary">工作时间：9:00-18:00</Text>
                </div>
              </div>
              <div className="contact-item">
                <MailOutlined className="contact-icon" />
                <div>
                  <Text strong>客服邮箱</Text>
                  <br />
                  <Text copyable><EMAIL></Text>
                  <br />
                  <Text type="secondary">24小时内回复</Text>
                </div>
              </div>
            </div>
          </div>

          <div className="action-buttons">
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchMerchantStatus}
            >
              刷新状态
            </Button>
            
            {merchantStatus.status === 'rejected' && (
              <Button 
                type="primary"
                onClick={handleResubmit}
              >
                重新申请
              </Button>
            )}
            
            {merchantStatus.status === 'approved' && (
              <Button 
                type="primary"
                onClick={() => navigate('/dashboard')}
              >
                进入管理后台
              </Button>
            )}
            
            <Button onClick={handleContactService}>
              联系客服
            </Button>
            
            <Button onClick={logout}>
              退出登录
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default MerchantStatusPage;
