.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-content {
  width: 100%;
  max-width: 800px;
}

.register-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: none;
  overflow: hidden;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 20px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  margin: -24px -24px 32px -24px;
}

.register-header h2 {
  color: #1890ff;
  margin-bottom: 8px;
}

.register-steps {
  margin-bottom: 32px;
}

.register-steps .ant-steps-item-title {
  font-size: 14px;
  font-weight: 500;
}

.register-steps .ant-steps-item-description {
  font-size: 12px;
  color: #8c8c8c;
}

.register-form {
  min-height: 400px;
}

.step-content {
  margin: 32px 0;
  min-height: 300px;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.step-actions .ant-btn {
  min-width: 100px;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
}

.register-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.register-footer .ant-btn-link {
  padding: 0;
  height: auto;
}

/* 确认信息样式 */
.confirm-info {
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.confirm-info h4 {
  color: #1890ff;
  margin-bottom: 20px;
  text-align: center;
}

.info-section {
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.info-section h5 {
  color: #262626;
  margin-bottom: 12px;
  font-weight: 600;
}

.info-section p {
  margin-bottom: 8px;
  color: #595959;
  line-height: 1.6;
}

.info-section p:last-child {
  margin-bottom: 0;
}

/* 表单样式优化 */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector,
.ant-input-number {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-affix-wrapper-focused,
.ant-select-focused .ant-select-selector,
.ant-input-number-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-input:hover,
.ant-input-affix-wrapper:hover,
.ant-select:hover .ant-select-selector,
.ant-input-number:hover {
  border-color: #40a9ff;
}

/* 按钮样式 */
.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 步骤条样式 */
.ant-steps-item-process .ant-steps-item-icon {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-steps-item-finish .ant-steps-item-icon {
  background: #52c41a;
  border-color: #52c41a;
}

.ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
  color: white;
}

/* 复选框样式 */
.ant-checkbox-wrapper {
  line-height: 1.6;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* 文本域样式 */
.ant-input {
  resize: vertical;
}

/* 选择器样式 */
.ant-select-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-container {
    padding: 16px;
  }
  
  .register-content {
    max-width: 100%;
  }
  
  .register-card {
    margin: 0;
  }
  
  .register-header {
    margin: -16px -16px 24px -16px;
    padding: 16px 0;
  }
  
  .register-steps {
    margin-bottom: 24px;
  }
  
  .step-content {
    margin: 24px 0;
    min-height: 250px;
  }
  
  .step-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .step-actions .ant-btn {
    width: 100%;
    max-width: 200px;
  }
  
  .register-steps .ant-steps-item-title {
    font-size: 12px;
  }
  
  .register-steps .ant-steps-item-description {
    display: none;
  }
}

@media (max-width: 576px) {
  .register-header h2 {
    font-size: 20px;
  }
  
  .info-section {
    padding: 12px;
  }
  
  .confirm-info {
    padding: 16px;
  }
  
  .step-content {
    min-height: 200px;
  }
}

/* 加载状态 */
.ant-btn-loading {
  pointer-events: none;
}

/* 错误提示样式 */
.ant-form-item-explain-error {
  font-size: 12px;
  margin-top: 4px;
}

/* 输入框前缀图标 */
.ant-input-prefix {
  color: #bfbfbc;
  margin-right: 8px;
}

.ant-input-affix-wrapper-focused .ant-input-prefix {
  color: #1890ff;
}

/* 链接样式 */
.ant-btn-link {
  color: #1890ff;
  text-decoration: none;
}

.ant-btn-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 步骤内容动画 */
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证成功状态 */
.ant-form-item-has-success .ant-input,
.ant-form-item-has-success .ant-input-affix-wrapper,
.ant-form-item-has-success .ant-select-selector {
  border-color: #52c41a;
}

/* 表单验证错误状态 */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-affix-wrapper,
.ant-form-item-has-error .ant-select-selector {
  border-color: #ff4d4f;
}

/* 禁用状态 */
.ant-input:disabled,
.ant-select-disabled .ant-select-selector {
  background-color: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}
