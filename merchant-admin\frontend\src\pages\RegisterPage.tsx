import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, Steps, Select, Upload, message, Row, Col, Checkbox } from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  MailOutlined, 
  PhoneOutlined, 
  ShopOutlined,
  FileTextOutlined,
  EnvironmentOutlined,
  UploadOutlined,
  IdcardOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';
import './RegisterPage.css';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;
const { Option } = Select;

interface RegisterFormData {
  // 基本信息
  merchant_name: string;
  business_name: string;
  business_license: string;
  contact_person: string;
  contact_phone: string;
  email: string;
  business_type: string;
  description?: string;
  
  // 地址信息
  province: string;
  city: string;
  district: string;
  detail_address: string;
  
  // 账户信息
  username: string;
  password: string;
  confirm_password: string;
  
  // 协议
  agreement: boolean;
}

const RegisterPage: React.FC = () => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<RegisterFormData>>({});
  const navigate = useNavigate();

  // 省市区数据（简化版）
  const locationData = {
    provinces: [
      { value: '北京市', label: '北京市' },
      { value: '上海市', label: '上海市' },
      { value: '广东省', label: '广东省' },
      { value: '浙江省', label: '浙江省' },
      { value: '江苏省', label: '江苏省' },
    ],
    cities: {
      '广东省': [
        { value: '广州市', label: '广州市' },
        { value: '深圳市', label: '深圳市' },
        { value: '东莞市', label: '东莞市' },
      ],
      '浙江省': [
        { value: '杭州市', label: '杭州市' },
        { value: '宁波市', label: '宁波市' },
      ],
    },
    districts: {
      '广州市': [
        { value: '天河区', label: '天河区' },
        { value: '越秀区', label: '越秀区' },
        { value: '海珠区', label: '海珠区' },
      ],
      '深圳市': [
        { value: '南山区', label: '南山区' },
        { value: '福田区', label: '福田区' },
        { value: '罗湖区', label: '罗湖区' },
      ],
    }
  };

  // 商家类型选项
  const businessTypes = [
    { value: 'restaurant', label: '餐厅' },
    { value: 'fast_food', label: '快餐' },
    { value: 'dessert', label: '甜品店' },
    { value: 'beverage', label: '饮品店' },
    { value: 'snack', label: '小食店' },
    { value: 'other', label: '其他' },
  ];

  // 步骤配置
  const steps = [
    {
      title: '基本信息',
      description: '填写商家基本信息',
    },
    {
      title: '地址信息',
      description: '填写经营地址',
    },
    {
      title: '账户设置',
      description: '设置登录账户',
    },
    {
      title: '完成注册',
      description: '确认信息并提交',
    },
  ];

  // 下一步
  const handleNext = async () => {
    try {
      const values = await form.validateFields();
      setFormData({ ...formData, ...values });
      setCurrentStep(currentStep + 1);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 上一步
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  // 提交注册
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const finalData = { ...formData, ...values };

      // 构造注册数据
      const registerData = {
        merchant_name: finalData.merchant_name!,
        business_name: finalData.business_name!,
        business_license: finalData.business_license!,
        contact_person: finalData.contact_person!,
        contact_phone: finalData.contact_phone!,
        email: finalData.email!,
        username: finalData.username!,
        password: finalData.password!,
        address: {
          province: finalData.province!,
          city: finalData.city!,
          district: finalData.district!,
          detail: finalData.detail_address!,
        },
        business_type: finalData.business_type!,
        description: finalData.description,
      };

      const response = await authService.register(registerData);

      if (response.success) {
        message.success('注册成功！请等待审核通过后登录');
        navigate('/register-success');
      } else {
        message.error(response.message || '注册失败，请重试');
      }
    } catch (error) {
      message.error('注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 渲染基本信息表单
  const renderBasicInfo = () => (
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item
          name="merchant_name"
          label="商家名称"
          rules={[
            { required: true, message: '请输入商家名称' },
            { min: 2, max: 50, message: '商家名称长度为2-50个字符' }
          ]}
        >
          <Input prefix={<ShopOutlined />} placeholder="请输入商家名称" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="business_name"
          label="营业执照名称"
          rules={[
            { required: true, message: '请输入营业执照名称' },
            { min: 2, max: 100, message: '营业执照名称长度为2-100个字符' }
          ]}
        >
          <Input prefix={<FileTextOutlined />} placeholder="请输入营业执照名称" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="business_license"
          label="营业执照号"
          rules={[
            { required: true, message: '请输入营业执照号' },
            { pattern: /^[0-9A-Z]{15,20}$/, message: '请输入有效的营业执照号' }
          ]}
        >
          <Input prefix={<IdcardOutlined />} placeholder="请输入营业执照号" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="business_type"
          label="商家类型"
          rules={[{ required: true, message: '请选择商家类型' }]}
        >
          <Select placeholder="请选择商家类型">
            {businessTypes.map(type => (
              <Option key={type.value} value={type.value}>{type.label}</Option>
            ))}
          </Select>
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="contact_person"
          label="联系人"
          rules={[
            { required: true, message: '请输入联系人姓名' },
            { min: 2, max: 20, message: '联系人姓名长度为2-20个字符' }
          ]}
        >
          <Input prefix={<UserOutlined />} placeholder="请输入联系人姓名" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="contact_phone"
          label="联系电话"
          rules={[
            { required: true, message: '请输入联系电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
          ]}
        >
          <Input prefix={<PhoneOutlined />} placeholder="请输入联系电话" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          name="email"
          label="邮箱地址"
          rules={[
            { required: true, message: '请输入邮箱地址' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]}
        >
          <Input prefix={<MailOutlined />} placeholder="请输入邮箱地址" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          name="description"
          label="商家描述"
          rules={[{ max: 500, message: '商家描述不能超过500个字符' }]}
        >
          <Input.TextArea 
            rows={3} 
            placeholder="请简单描述您的商家特色（选填）" 
            showCount 
            maxLength={500}
          />
        </Form.Item>
      </Col>
    </Row>
  );

  // 渲染地址信息表单
  const renderAddressInfo = () => (
    <Row gutter={16}>
      <Col span={8}>
        <Form.Item
          name="province"
          label="省份"
          rules={[{ required: true, message: '请选择省份' }]}
        >
          <Select 
            placeholder="请选择省份"
            onChange={() => {
              form.setFieldsValue({ city: undefined, district: undefined });
            }}
          >
            {locationData.provinces.map(province => (
              <Option key={province.value} value={province.value}>{province.label}</Option>
            ))}
          </Select>
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          name="city"
          label="城市"
          rules={[{ required: true, message: '请选择城市' }]}
        >
          <Select 
            placeholder="请选择城市"
            onChange={() => {
              form.setFieldsValue({ district: undefined });
            }}
          >
            {(locationData.cities[form.getFieldValue('province')] || []).map(city => (
              <Option key={city.value} value={city.value}>{city.label}</Option>
            ))}
          </Select>
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          name="district"
          label="区县"
          rules={[{ required: true, message: '请选择区县' }]}
        >
          <Select placeholder="请选择区县">
            {(locationData.districts[form.getFieldValue('city')] || []).map(district => (
              <Option key={district.value} value={district.value}>{district.label}</Option>
            ))}
          </Select>
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          name="detail_address"
          label="详细地址"
          rules={[
            { required: true, message: '请输入详细地址' },
            { min: 5, max: 200, message: '详细地址长度为5-200个字符' }
          ]}
        >
          <Input.TextArea 
            rows={3}
            prefix={<EnvironmentOutlined />} 
            placeholder="请输入详细地址（街道、门牌号等）" 
            showCount 
            maxLength={200}
          />
        </Form.Item>
      </Col>
    </Row>
  );

  // 渲染账户设置表单
  const renderAccountInfo = () => (
    <Row gutter={16}>
      <Col span={24}>
        <Form.Item
          name="username"
          label="用户名"
          rules={[
            { required: true, message: '请输入用户名' },
            { min: 3, max: 20, message: '用户名长度为3-20个字符' },
            { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
          ]}
        >
          <Input prefix={<UserOutlined />} placeholder="请输入用户名" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 6, max: 20, message: '密码长度为6-20个字符' },
            { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字' }
          ]}
        >
          <Input.Password prefix={<LockOutlined />} placeholder="请输入密码" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="confirm_password"
          label="确认密码"
          dependencies={['password']}
          rules={[
            { required: true, message: '请确认密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password prefix={<LockOutlined />} placeholder="请再次输入密码" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          name="agreement"
          valuePropName="checked"
          rules={[
            { 
              validator: (_, value) => 
                value ? Promise.resolve() : Promise.reject(new Error('请阅读并同意服务协议'))
            }
          ]}
        >
          <Checkbox>
            我已阅读并同意 
            <Button type="link" onClick={() => window.open('/terms')}>
              《服务协议》
            </Button> 
            和 
            <Button type="link" onClick={() => window.open('/privacy')}>
              《隐私政策》
            </Button>
          </Checkbox>
        </Form.Item>
      </Col>
    </Row>
  );

  // 渲染确认信息
  const renderConfirmInfo = () => (
    <div className="confirm-info">
      <Title level={4}>请确认您的注册信息</Title>
      <div className="info-section">
        <Title level={5}>基本信息</Title>
        <p><strong>商家名称：</strong>{formData.merchant_name}</p>
        <p><strong>营业执照名称：</strong>{formData.business_name}</p>
        <p><strong>营业执照号：</strong>{formData.business_license}</p>
        <p><strong>商家类型：</strong>{businessTypes.find(t => t.value === formData.business_type)?.label}</p>
        <p><strong>联系人：</strong>{formData.contact_person}</p>
        <p><strong>联系电话：</strong>{formData.contact_phone}</p>
        <p><strong>邮箱：</strong>{formData.email}</p>
      </div>
      <div className="info-section">
        <Title level={5}>地址信息</Title>
        <p><strong>地址：</strong>{formData.province} {formData.city} {formData.district} {formData.detail_address}</p>
      </div>
      <div className="info-section">
        <Title level={5}>账户信息</Title>
        <p><strong>用户名：</strong>{formData.username}</p>
      </div>
    </div>
  );

  return (
    <div className="register-container">
      <div className="register-content">
        <Card className="register-card">
          <div className="register-header">
            <Title level={2}>商家入驻</Title>
            <Text type="secondary">加入我们的平台，开启您的线上经营之路</Text>
          </div>

          <Steps current={currentStep} className="register-steps">
            {steps.map(item => (
              <Step key={item.title} title={item.title} description={item.description} />
            ))}
          </Steps>

          <Form
            form={form}
            layout="vertical"
            className="register-form"
            initialValues={formData}
          >
            <div className="step-content">
              {currentStep === 0 && renderBasicInfo()}
              {currentStep === 1 && renderAddressInfo()}
              {currentStep === 2 && renderAccountInfo()}
              {currentStep === 3 && renderConfirmInfo()}
            </div>

            <div className="step-actions">
              {currentStep > 0 && (
                <Button onClick={handlePrev}>
                  上一步
                </Button>
              )}
              {currentStep < steps.length - 1 && (
                <Button type="primary" onClick={handleNext}>
                  下一步
                </Button>
              )}
              {currentStep === steps.length - 1 && (
                <Button type="primary" loading={loading} onClick={handleSubmit}>
                  提交注册
                </Button>
              )}
            </div>
          </Form>

          <div className="register-footer">
            <Text type="secondary">
              已有账户？
              <Button type="link" onClick={() => navigate('/login')}>
                立即登录
              </Button>
            </Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default RegisterPage;
