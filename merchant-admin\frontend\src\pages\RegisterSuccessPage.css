.register-success-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-success-content {
  width: 100%;
  max-width: 800px;
}

.success-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: none;
  overflow: hidden;
}

.success-card .ant-result {
  padding: 40px 24px 24px;
}

.success-card .ant-result-title {
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.success-card .ant-result-subtitle {
  color: #595959;
  font-size: 16px;
  margin-top: 8px;
}

.success-card .ant-result-icon {
  font-size: 72px;
  margin-bottom: 24px;
}

.process-info {
  margin: 32px 0;
  padding: 24px;
  background: #fafafa;
  border-radius: 8px;
}

.process-info h4 {
  color: #262626;
  margin-bottom: 20px;
  text-align: center;
}

.process-info .ant-timeline {
  margin-top: 20px;
}

.process-info .ant-timeline-item-content {
  margin-left: 16px;
}

.process-info .ant-timeline-item-content strong {
  color: #262626;
  font-size: 14px;
}

.process-info .ant-timeline-item-content .ant-typography {
  font-size: 12px;
  margin-top: 4px;
}

.reminder-alert {
  margin: 24px 0;
  border-radius: 8px;
}

.reminder-alert .ant-alert-message {
  font-weight: 600;
  color: #262626;
}

.reminder-alert .ant-alert-description {
  margin-top: 8px;
}

.reminder-alert ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.reminder-alert li {
  margin-bottom: 4px;
  color: #595959;
}

.contact-info {
  margin: 32px 0;
  padding: 24px;
  background: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.contact-info h4 {
  color: #262626;
  margin-bottom: 20px;
}

.contact-methods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.contact-icon {
  font-size: 20px;
  color: #1890ff;
  margin-top: 4px;
}

.contact-item strong {
  color: #262626;
  font-size: 14px;
}

.contact-item .ant-typography {
  font-size: 13px;
  margin-top: 2px;
}

.next-steps {
  margin: 32px 0;
}

.next-steps h4 {
  color: #262626;
  margin-bottom: 20px;
  text-align: center;
}

.action-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.action-card {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 24px;
  color: #1890ff;
  margin-bottom: 8px;
  display: block;
}

.action-card strong {
  color: #262626;
  font-size: 14px;
  display: block;
  margin-bottom: 4px;
}

.action-card .ant-typography {
  font-size: 12px;
  color: #8c8c8c;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.action-buttons .ant-btn {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
}

.action-buttons .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.action-buttons .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-success-container {
    padding: 16px;
  }
  
  .register-success-content {
    max-width: 100%;
  }
  
  .success-card .ant-result {
    padding: 32px 16px 16px;
  }
  
  .success-card .ant-result-title {
    font-size: 20px;
  }
  
  .success-card .ant-result-subtitle {
    font-size: 14px;
  }
  
  .success-card .ant-result-icon {
    font-size: 60px;
    margin-bottom: 20px;
  }
  
  .process-info,
  .contact-info {
    margin: 24px 0;
    padding: 20px;
  }
  
  .contact-methods {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .action-cards {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-buttons .ant-btn {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 576px) {
  .success-card .ant-result-title {
    font-size: 18px;
  }
  
  .success-card .ant-result-subtitle {
    font-size: 13px;
  }
  
  .process-info,
  .contact-info {
    padding: 16px;
  }
  
  .contact-item {
    gap: 8px;
  }
  
  .contact-icon {
    font-size: 18px;
  }
  
  .action-icon {
    font-size: 20px;
  }
}

/* 时间线样式优化 */
.ant-timeline-item-head {
  background: white;
  border: 2px solid;
}

.ant-timeline-item-tail {
  border-left: 2px solid #f0f0f0;
}

/* 复制按钮样式 */
.ant-typography-copy {
  margin-left: 8px;
  color: #1890ff;
}

.ant-typography-copy:hover {
  color: #40a9ff;
}

/* 警告框样式 */
.ant-alert-info {
  border: 1px solid #91d5ff;
  background: #e6f7ff;
}

.ant-alert-info .ant-alert-icon {
  color: #1890ff;
}

/* 卡片悬停效果 */
.success-card {
  transition: all 0.3s ease;
}

/* 文本选择样式 */
::selection {
  background: #bae7ff;
  color: #262626;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
