import React from 'react';
import { Result, But<PERSON>, Card, Typography, Timeline, Alert } from 'antd';
import { 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  PhoneOutlined, 
  MailOutlined,
  FileTextOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import './RegisterSuccessPage.css';

const { Title, Text, Paragraph } = Typography;

const RegisterSuccessPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="register-success-container">
      <div className="register-success-content">
        <Card className="success-card">
          <Result
            status="success"
            title="注册申请提交成功！"
            subTitle="您的商家入驻申请已成功提交，我们将在1-3个工作日内完成审核。"
            icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          />

          <div className="process-info">
            <Title level={4}>审核流程</Title>
            <Timeline
              items={[
                {
                  color: 'green',
                  dot: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
                  children: (
                    <div>
                      <Text strong>提交申请</Text>
                      <br />
                      <Text type="secondary">您已成功提交商家入驻申请</Text>
                    </div>
                  ),
                },
                {
                  color: 'blue',
                  dot: <ClockCircleOutlined style={{ color: '#1890ff' }} />,
                  children: (
                    <div>
                      <Text strong>资料审核</Text>
                      <br />
                      <Text type="secondary">我们将审核您提交的商家资料和证件</Text>
                    </div>
                  ),
                },
                {
                  color: 'gray',
                  children: (
                    <div>
                      <Text strong>实地核验</Text>
                      <br />
                      <Text type="secondary">必要时我们会安排工作人员进行实地核验</Text>
                    </div>
                  ),
                },
                {
                  color: 'gray',
                  children: (
                    <div>
                      <Text strong>审核完成</Text>
                      <br />
                      <Text type="secondary">审核通过后您将收到通知，可以开始使用系统</Text>
                    </div>
                  ),
                },
              ]}
            />
          </div>

          <Alert
            message="重要提醒"
            description={
              <div>
                <Paragraph>
                  <Text strong>请确保您提供的联系方式畅通：</Text>
                </Paragraph>
                <ul>
                  <li>我们可能会通过电话联系您核实信息</li>
                  <li>审核结果将通过邮件和短信通知您</li>
                  <li>如有疑问，请及时联系客服</li>
                </ul>
              </div>
            }
            type="info"
            showIcon
            className="reminder-alert"
          />

          <div className="contact-info">
            <Title level={4}>联系我们</Title>
            <div className="contact-methods">
              <div className="contact-item">
                <PhoneOutlined className="contact-icon" />
                <div>
                  <Text strong>客服热线</Text>
                  <br />
                  <Text copyable>************</Text>
                  <br />
                  <Text type="secondary">工作时间：9:00-18:00</Text>
                </div>
              </div>
              <div className="contact-item">
                <MailOutlined className="contact-icon" />
                <div>
                  <Text strong>客服邮箱</Text>
                  <br />
                  <Text copyable><EMAIL></Text>
                  <br />
                  <Text type="secondary">24小时内回复</Text>
                </div>
              </div>
            </div>
          </div>

          <div className="next-steps">
            <Title level={4}>接下来您可以</Title>
            <div className="action-cards">
              <Card size="small" className="action-card">
                <FileTextOutlined className="action-icon" />
                <Text strong>准备开店资料</Text>
                <br />
                <Text type="secondary">准备商品图片、菜单等开店必需资料</Text>
              </Card>
              <Card size="small" className="action-card">
                <UserOutlined className="action-icon" />
                <Text strong>了解平台规则</Text>
                <br />
                <Text type="secondary">阅读商家运营手册和平台规则</Text>
              </Card>
            </div>
          </div>

          <div className="action-buttons">
            <Button 
              type="primary" 
              size="large"
              onClick={() => navigate('/login')}
            >
              返回登录
            </Button>
            <Button 
              size="large"
              onClick={() => window.open('/merchant-guide')}
            >
              查看商家指南
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default RegisterSuccessPage;
