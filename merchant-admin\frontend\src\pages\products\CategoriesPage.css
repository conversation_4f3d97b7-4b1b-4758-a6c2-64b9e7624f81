.categories-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-header h3 {
  margin: 0;
  color: #262626;
}

.image-placeholder {
  width: 60px;
  height: 60px;
  background: #f5f5f5;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bfbfbf;
  font-size: 16px;
}

/* 拖拽表格行样式 */
.ant-table-tbody > tr.drop-over-downward td {
  border-bottom: 2px dashed #1890ff;
}

.ant-table-tbody > tr.drop-over-upward td {
  border-top: 2px dashed #1890ff;
}

.ant-table-tbody > tr:hover {
  background: #f5f5f5;
}

.ant-table-tbody > tr.dragging {
  background: #e6f7ff;
  opacity: 0.8;
}

/* 表格列样式 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
}

.ant-table-tbody > tr > td {
  padding: 16px;
  vertical-align: middle;
}

/* 操作按钮样式 */
.ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 14px;
}

.ant-btn-link:hover {
  background: none;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  border: none;
}

/* 图片样式 */
.ant-image {
  border-radius: 4px;
  overflow: hidden;
}

.ant-image-img {
  object-fit: cover;
}

/* 上传组件样式 */
.ant-upload-select-picture-card {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
  background: #fafafa;
  transition: all 0.3s ease;
}

.ant-upload-select-picture-card:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.ant-upload-list-picture-card .ant-upload-list-item {
  width: 100px;
  height: 100px;
  border-radius: 6px;
}

/* 表单样式 */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-number,
.ant-select-selector {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 开关样式 */
.ant-switch {
  border-radius: 12px;
}

.ant-switch-checked {
  background: #52c41a;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
  font-size: 16px;
}

.ant-modal-body {
  padding: 24px;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
}

/* 确认弹窗样式 */
.ant-popconfirm-inner {
  border-radius: 8px;
}

.ant-popconfirm-message {
  margin-bottom: 8px;
}

.ant-popconfirm-description {
  color: #8c8c8c;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-header .ant-space {
    width: 100%;
    justify-content: flex-end;
  }
  
  .ant-table-tbody > tr > td {
    padding: 12px 8px;
  }
  
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .ant-upload-select-picture-card {
    width: 80px;
    height: 80px;
  }
  
  .ant-upload-list-picture-card .ant-upload-list-item {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 576px) {
  .categories-page {
    padding: 0;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .image-placeholder {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
  
  .ant-image {
    width: 40px !important;
    height: 40px !important;
  }
  
  .ant-btn-link {
    font-size: 12px;
  }
  
  .ant-tag {
    font-size: 11px;
    padding: 1px 6px;
  }
}

/* 拖拽指示器 */
.drag-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: #1890ff;
  z-index: 999;
}

.drag-indicator.top {
  top: -1px;
}

.drag-indicator.bottom {
  bottom: -1px;
}

/* 空状态样式 */
.ant-empty {
  padding: 40px 0;
}

.ant-empty-description {
  color: #8c8c8c;
}

/* 加载状态 */
.ant-table-placeholder {
  padding: 40px 0;
}

.ant-spin-container {
  min-height: 200px;
}

/* 表格滚动条 */
.ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 文本省略 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 成功状态 */
.success-text {
  color: #52c41a;
}

/* 警告状态 */
.warning-text {
  color: #fa8c16;
}

/* 错误状态 */
.error-text {
  color: #ff4d4f;
}

/* 次要文本 */
.secondary-text {
  color: #8c8c8c;
  font-size: 12px;
}

/* 卡片内边距调整 */
.ant-card-body {
  padding: 24px;
}

/* 按钮组间距 */
.ant-space-item {
  display: flex;
  align-items: center;
}

/* 表格行悬停效果 */
.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
  transition: background 0.3s ease;
}

/* 拖拽时的视觉反馈 */
.dragging-row {
  opacity: 0.5;
  transform: rotate(5deg);
  transition: all 0.3s ease;
}

/* 排序按钮样式 */
.sort-handle {
  cursor: move;
  color: #999;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.sort-handle:hover {
  color: #1890ff;
  background: #f0f8ff;
}
