import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Upload,
  Switch,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Image,
  Typography
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  DragOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { productService } from '../../services/productService';
import { Category } from '../../types';
import './CategoriesPage.css';

const { Title } = Typography;
const { TextArea } = Input;

interface DragItem {
  index: number;
  id: number;
  type: string;
}

const DraggableRow: React.FC<{
  index: number;
  moveRow: (dragIndex: number, hoverIndex: number) => void;
  children: React.ReactNode;
}> = ({ index, moveRow, children, ...restProps }) => {
  const ref = React.useRef<HTMLTableRowElement>(null);
  
  const [{ handlerId }, drop] = useDrop({
    accept: 'row',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) {
        return;
      }

      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = (clientOffset as any).y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      moveRow(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: 'row',
    item: () => {
      return { id: (restProps as any).id, index };
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0 : 1;
  drag(drop(ref));

  return (
    <tr
      ref={ref}
      style={{ cursor: 'move', opacity }}
      data-handler-id={handlerId}
      {...restProps}
    >
      {children}
    </tr>
  );
};

const CategoriesPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await productService.categories.getList();
      if (response.success) {
        setCategories(response.data || []);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('获取分类列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    form.setFieldsValue(category);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await productService.categories.delete(id);
      if (response.success) {
        message.success('删除成功');
        fetchCategories();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const response = editingCategory
        ? await productService.categories.update(editingCategory.id, values)
        : await productService.categories.create(values);

      if (response.success) {
        message.success(editingCategory ? '更新成功' : '创建成功');
        setModalVisible(false);
        fetchCategories();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  const moveRow = (dragIndex: number, hoverIndex: number) => {
    const dragRow = categories[dragIndex];
    const newCategories = [...categories];
    newCategories.splice(dragIndex, 1);
    newCategories.splice(hoverIndex, 0, dragRow);
    setCategories(newCategories);
  };

  const handleSaveSort = async () => {
    try {
      const items = categories.map((category, index) => ({
        id: category.id,
        sort_order: index + 1
      }));
      
      const response = await productService.categories.updateSort(items);
      if (response.success) {
        message.success('排序保存成功');
        fetchCategories();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('保存排序失败');
    }
  };

  const columns = [
    {
      title: '排序',
      dataIndex: 'sort_order',
      width: 80,
      render: () => <DragOutlined style={{ cursor: 'move', color: '#999' }} />
    },
    {
      title: '分类图片',
      dataIndex: 'image',
      width: 100,
      render: (image: string) => (
        image ? (
          <Image
            width={60}
            height={60}
            src={image}
            style={{ borderRadius: 4, objectFit: 'cover' }}
            fallback="/images/placeholder.png"
          />
        ) : (
          <div className="image-placeholder">
            <EyeOutlined />
          </div>
        )
      )
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      render: (name: string, record: Category) => (
        <div>
          <div style={{ fontWeight: 500 }}>{name}</div>
          {record.description && (
            <div style={{ fontSize: 12, color: '#999', marginTop: 2 }}>
              {record.description}
            </div>
          )}
        </div>
      )
    },
    {
      title: '商品数量',
      dataIndex: 'product_count',
      width: 100,
      render: (count: number) => (
        <Tag color={count > 0 ? 'blue' : 'default'}>{count}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 150,
      render: (time: string) => time?.slice(0, 16)
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: Category) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分类吗？"
            description="删除后不可恢复，请谨慎操作"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  const components = {
    body: {
      row: DraggableRow,
    },
  };

  return (
    <div className="categories-page">
      <Card>
        <div className="page-header">
          <Title level={3}>分类管理</Title>
          <Space>
            <Button onClick={handleSaveSort}>
              保存排序
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加分类
            </Button>
          </Space>
        </div>

        <DndProvider backend={HTML5Backend}>
          <Table
            components={components}
            rowKey="id"
            columns={columns}
            dataSource={categories}
            loading={loading}
            pagination={false}
            onRow={(record, index) => ({
              index,
              moveRow,
              id: record.id,
            })}
          />
        </DndProvider>
      </Card>

      <Modal
        title={editingCategory ? '编辑分类' : '添加分类'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[
              { required: true, message: '请输入分类名称' },
              { max: 50, message: '分类名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="分类描述"
            rules={[{ max: 200, message: '描述不能超过200个字符' }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入分类描述（可选）"
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item
            name="image"
            label="分类图片"
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              onChange={(info) => {
                if (info.fileList.length > 0) {
                  // 这里应该调用上传接口
                  form.setFieldsValue({ image: '/images/categories/mock.jpg' });
                } else {
                  form.setFieldsValue({ image: undefined });
                }
              }}
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item
            name="sort_order"
            label="排序"
            rules={[{ type: 'number', min: 0, message: '排序值不能小于0' }]}
          >
            <InputNumber
              min={0}
              placeholder="数值越小排序越靠前"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoriesPage;
