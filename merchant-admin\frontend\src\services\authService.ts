import axios from 'axios';
import { MerchantUser, ApiResponse } from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || '/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('merchant_access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理token过期
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      const refreshToken = localStorage.getItem('merchant_refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken
          });
          
          const { access_token } = response.data;
          localStorage.setItem('merchant_access_token', access_token);
          
          // 重试原请求
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return api(originalRequest);
        } catch (refreshError) {
          // 刷新失败，清除token并跳转到登录页
          localStorage.removeItem('merchant_access_token');
          localStorage.removeItem('merchant_refresh_token');
          localStorage.removeItem('merchant_user');
          window.location.href = '/login';
        }
      } else {
        // 没有refresh token，跳转到登录页
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

export const authService = {
  // 商家登录
  login: async (credentials: { username: string; password: string; merchant_id?: number }) => {
    try {
      const response = await api.post('/auth/merchant/login', credentials);
      return response.data;
    } catch (error: any) {
      // 如果后端不可用，使用模拟登录
      console.warn('后端不可用，使用模拟登录');

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟登录验证
      if (credentials.username === 'admin' && credentials.password === '123456') {
        return {
          success: true,
          message: '登录成功',
          data: {
            user: {
              id: 1,
              merchant_id: 1,
              username: 'admin',
              email: '<EMAIL>',
              role: 'owner',
              permissions: ['all'],
              is_active: true
            },
            access_token: `mock_token_${Date.now()}`,
            refresh_token: `mock_refresh_${Date.now()}`,
            merchant: {
              id: 1,
              name: '测试餐厅',
              status: 'approved',
              is_active: true
            }
          }
        };
      } else {
        return {
          success: false,
          message: '用户名或密码错误'
        };
      }
    }
  },

  // 商家注册
  register: async (data: {
    merchant_name: string;
    business_name: string;
    business_license: string;
    contact_person: string;
    contact_phone: string;
    email: string;
    username: string;
    password: string;
    address: {
      province: string;
      city: string;
      district: string;
      detail: string;
    };
    business_type: string;
  }) => {
    try {
      const response = await api.post('/auth/merchant/register', data);
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '注册失败，请稍后重试'
      };
    }
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<ApiResponse<MerchantUser>> => {
    try {
      const response = await api.get('/auth/merchant/profile');
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '获取用户信息失败'
      };
    }
  },

  // 更新用户信息
  updateProfile: async (data: Partial<MerchantUser>) => {
    try {
      const response = await api.put('/auth/merchant/profile', data);
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '更新失败，请稍后重试'
      };
    }
  },

  // 修改密码
  changePassword: async (data: { old_password: string; new_password: string }) => {
    try {
      const response = await api.put('/auth/merchant/password', data);
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '修改密码失败，请稍后重试'
      };
    }
  },

  // 退出登录
  logout: async () => {
    try {
      await api.post('/auth/merchant/logout');
    } catch (error) {
      // 忽略退出登录的错误
    } finally {
      // 清除本地存储
      localStorage.removeItem('merchant_access_token');
      localStorage.removeItem('merchant_refresh_token');
      localStorage.removeItem('merchant_user');
    }
  },

  // 发送验证码
  sendVerificationCode: async (phone: string) => {
    try {
      const response = await api.post('/auth/send-code', { phone });
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '发送验证码失败，请稍后重试'
      };
    }
  },

  // 验证码登录
  loginWithCode: async (phone: string, code: string) => {
    try {
      const response = await api.post('/auth/merchant/login-code', { phone, code });
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '登录失败，请稍后重试'
      };
    }
  },

  // 忘记密码
  forgotPassword: async (email: string) => {
    try {
      const response = await api.post('/auth/forgot-password', { email });
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '发送重置邮件失败，请稍后重试'
      };
    }
  },

  // 重置密码
  resetPassword: async (token: string, password: string) => {
    try {
      const response = await api.post('/auth/reset-password', { token, password });
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '重置密码失败，请稍后重试'
      };
    }
  },

  // 检查商家状态
  checkMerchantStatus: async () => {
    try {
      const response = await api.get('/auth/merchant/status');
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: '获取商家状态失败'
      };
    }
  }
};

export default api;
