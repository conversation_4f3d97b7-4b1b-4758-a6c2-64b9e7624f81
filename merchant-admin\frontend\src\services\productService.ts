import api from './authService';
import { Category, Product, ProductVariant, ApiResponse, PaginationParams } from '../types';

export const productService = {
  // 分类管理
  categories: {
    // 获取分类列表
    getList: async (): Promise<ApiResponse<Category[]>> => {
      try {
        const response = await api.get('/products/categories');
        return response.data;
      } catch (error: any) {
        // 模拟数据
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          message: '获取成功',
          data: [
            {
              id: 1,
              merchant_id: 1,
              name: '主食类',
              description: '米饭、面条等主食',
              image: '/images/categories/staple.jpg',
              sort_order: 1,
              is_active: true,
              created_at: '2024-12-12 10:00:00',
              updated_at: '2024-12-12 10:00:00',
              product_count: 15
            },
            {
              id: 2,
              merchant_id: 1,
              name: '家常菜',
              description: '经典家常菜品',
              image: '/images/categories/home-style.jpg',
              sort_order: 2,
              is_active: true,
              created_at: '2024-12-12 10:00:00',
              updated_at: '2024-12-12 10:00:00',
              product_count: 25
            },
            {
              id: 3,
              merchant_id: 1,
              name: '烘焙甜点',
              description: '蛋糕、面包等烘焙产品',
              image: '/images/categories/dessert.jpg',
              sort_order: 3,
              is_active: true,
              created_at: '2024-12-12 10:00:00',
              updated_at: '2024-12-12 10:00:00',
              product_count: 12
            }
          ]
        };
      }
    },

    // 创建分类
    create: async (data: Partial<Category>): Promise<ApiResponse<Category>> => {
      try {
        const response = await api.post('/products/categories', data);
        return response.data;
      } catch (error: any) {
        // 模拟创建
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          success: true,
          message: '创建成功',
          data: {
            id: Date.now(),
            merchant_id: 1,
            name: data.name!,
            description: data.description,
            image: data.image,
            sort_order: data.sort_order || 0,
            is_active: data.is_active !== false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          }
        };
      }
    },

    // 更新分类
    update: async (id: number, data: Partial<Category>): Promise<ApiResponse<Category>> => {
      try {
        const response = await api.put(`/products/categories/${id}`, data);
        return response.data;
      } catch (error: any) {
        // 模拟更新
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          success: true,
          message: '更新成功',
          data: {
            id,
            merchant_id: 1,
            name: data.name!,
            description: data.description,
            image: data.image,
            sort_order: data.sort_order || 0,
            is_active: data.is_active !== false,
            created_at: '2024-12-12 10:00:00',
            updated_at: new Date().toISOString(),
            product_count: 0
          }
        };
      }
    },

    // 删除分类
    delete: async (id: number): Promise<ApiResponse> => {
      try {
        const response = await api.delete(`/products/categories/${id}`);
        return response.data;
      } catch (error: any) {
        // 模拟删除
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          message: '删除成功'
        };
      }
    },

    // 更新排序
    updateSort: async (items: { id: number; sort_order: number }[]): Promise<ApiResponse> => {
      try {
        const response = await api.put('/products/categories/sort', { items });
        return response.data;
      } catch (error: any) {
        // 模拟排序更新
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          message: '排序更新成功'
        };
      }
    }
  },

  // 商品管理
  products: {
    // 获取商品列表
    getList: async (params?: PaginationParams & { category_id?: number; status?: string }): Promise<ApiResponse<Product[]>> => {
      try {
        const response = await api.get('/products', { params });
        return response.data;
      } catch (error: any) {
        // 模拟数据
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          success: true,
          message: '获取成功',
          data: [
            {
              id: 1,
              merchant_id: 1,
              category_id: 1,
              name: '宫保鸡丁',
              description: '经典川菜，鸡肉嫩滑，花生香脆',
              images: ['/images/products/gongbao.jpg'],
              price: 28.00,
              original_price: 32.00,
              cost_price: 15.00,
              stock_quantity: 50,
              min_stock: 10,
              max_stock: 100,
              sales_count: 156,
              view_count: 1250,
              favorite_count: 89,
              unit: '份',
              weight: 300,
              calories: 280,
              ingredients: '鸡肉、花生、青椒、红椒',
              allergens: '花生',
              tags: ['招牌菜', '下饭菜'],
              is_spicy: true,
              is_vegetarian: false,
              is_recommended: true,
              is_new: false,
              status: 'active',
              sort_order: 1,
              rating: 4.8,
              rating_count: 89,
              created_at: '2024-12-12 10:00:00',
              updated_at: '2024-12-12 10:00:00',
              category_name: '家常菜',
              variants: []
            },
            {
              id: 2,
              merchant_id: 1,
              category_id: 2,
              name: '麻婆豆腐',
              description: '四川传统名菜，麻辣鲜香',
              images: ['/images/products/mapo.jpg'],
              price: 18.00,
              original_price: 22.00,
              cost_price: 8.00,
              stock_quantity: 30,
              min_stock: 5,
              max_stock: 80,
              sales_count: 98,
              view_count: 890,
              favorite_count: 56,
              unit: '份',
              weight: 250,
              calories: 180,
              ingredients: '豆腐、肉末、豆瓣酱',
              allergens: '大豆',
              tags: ['经典菜', '素食可选'],
              is_spicy: true,
              is_vegetarian: false,
              is_recommended: false,
              is_new: false,
              status: 'active',
              sort_order: 2,
              rating: 4.6,
              rating_count: 67,
              created_at: '2024-12-12 10:00:00',
              updated_at: '2024-12-12 10:00:00',
              category_name: '家常菜',
              variants: []
            }
          ],
          total: 45,
          page: 1,
          per_page: 20
        };
      }
    },

    // 获取商品详情
    getDetail: async (id: number): Promise<ApiResponse<Product>> => {
      try {
        const response = await api.get(`/products/${id}`);
        return response.data;
      } catch (error: any) {
        // 模拟数据
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          message: '获取成功',
          data: {
            id,
            merchant_id: 1,
            category_id: 1,
            name: '宫保鸡丁',
            description: '经典川菜，鸡肉嫩滑，花生香脆，口感丰富',
            images: ['/images/products/gongbao1.jpg', '/images/products/gongbao2.jpg'],
            price: 28.00,
            original_price: 32.00,
            cost_price: 15.00,
            stock_quantity: 50,
            min_stock: 10,
            max_stock: 100,
            sales_count: 156,
            view_count: 1250,
            favorite_count: 89,
            unit: '份',
            weight: 300,
            calories: 280,
            ingredients: '鸡肉、花生、青椒、红椒、干辣椒、花椒',
            allergens: '花生',
            tags: ['招牌菜', '下饭菜', '川菜'],
            is_spicy: true,
            is_vegetarian: false,
            is_recommended: true,
            is_new: false,
            status: 'active',
            sort_order: 1,
            rating: 4.8,
            rating_count: 89,
            created_at: '2024-12-12 10:00:00',
            updated_at: '2024-12-12 10:00:00',
            category_name: '家常菜',
            variants: [
              {
                id: 1,
                product_id: id,
                name: '小份',
                description: '适合1-2人食用',
                price_adjustment: -5.00,
                stock_quantity: 20,
                is_active: true,
                sort_order: 1,
                created_at: '2024-12-12 10:00:00',
                updated_at: '2024-12-12 10:00:00'
              },
              {
                id: 2,
                product_id: id,
                name: '大份',
                description: '适合3-4人食用',
                price_adjustment: 8.00,
                stock_quantity: 15,
                is_active: true,
                sort_order: 2,
                created_at: '2024-12-12 10:00:00',
                updated_at: '2024-12-12 10:00:00'
              }
            ]
          }
        };
      }
    },

    // 创建商品
    create: async (data: Partial<Product>): Promise<ApiResponse<Product>> => {
      try {
        const response = await api.post('/products', data);
        return response.data;
      } catch (error: any) {
        // 模拟创建
        await new Promise(resolve => setTimeout(resolve, 1000));
        return {
          success: true,
          message: '创建成功',
          data: {
            id: Date.now(),
            merchant_id: 1,
            category_id: data.category_id!,
            name: data.name!,
            description: data.description,
            images: data.images || [],
            price: data.price!,
            original_price: data.original_price,
            cost_price: data.cost_price,
            stock_quantity: data.stock_quantity || 0,
            min_stock: data.min_stock || 0,
            max_stock: data.max_stock || 999,
            sales_count: 0,
            view_count: 0,
            favorite_count: 0,
            unit: data.unit || '份',
            weight: data.weight,
            calories: data.calories,
            ingredients: data.ingredients,
            allergens: data.allergens,
            tags: data.tags || [],
            is_spicy: data.is_spicy || false,
            is_vegetarian: data.is_vegetarian || false,
            is_recommended: data.is_recommended || false,
            is_new: data.is_new || false,
            status: data.status || 'active',
            sort_order: data.sort_order || 0,
            rating: 5.0,
            rating_count: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            category_name: '未知分类',
            variants: []
          }
        };
      }
    },

    // 更新商品
    update: async (id: number, data: Partial<Product>): Promise<ApiResponse<Product>> => {
      try {
        const response = await api.put(`/products/${id}`, data);
        return response.data;
      } catch (error: any) {
        // 模拟更新
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          success: true,
          message: '更新成功',
          data: {
            id,
            merchant_id: 1,
            category_id: data.category_id!,
            name: data.name!,
            description: data.description,
            images: data.images || [],
            price: data.price!,
            original_price: data.original_price,
            cost_price: data.cost_price,
            stock_quantity: data.stock_quantity || 0,
            min_stock: data.min_stock || 0,
            max_stock: data.max_stock || 999,
            sales_count: 156,
            view_count: 1250,
            favorite_count: 89,
            unit: data.unit || '份',
            weight: data.weight,
            calories: data.calories,
            ingredients: data.ingredients,
            allergens: data.allergens,
            tags: data.tags || [],
            is_spicy: data.is_spicy || false,
            is_vegetarian: data.is_vegetarian || false,
            is_recommended: data.is_recommended || false,
            is_new: data.is_new || false,
            status: data.status || 'active',
            sort_order: data.sort_order || 0,
            rating: 4.8,
            rating_count: 89,
            created_at: '2024-12-12 10:00:00',
            updated_at: new Date().toISOString(),
            category_name: '家常菜',
            variants: []
          }
        };
      }
    },

    // 删除商品
    delete: async (id: number): Promise<ApiResponse> => {
      try {
        const response = await api.delete(`/products/${id}`);
        return response.data;
      } catch (error: any) {
        // 模拟删除
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          message: '删除成功'
        };
      }
    },

    // 批量更新状态
    batchUpdateStatus: async (ids: number[], status: string): Promise<ApiResponse> => {
      try {
        const response = await api.put('/products/batch-status', { ids, status });
        return response.data;
      } catch (error: any) {
        // 模拟批量更新
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          success: true,
          message: `已${status === 'active' ? '上架' : '下架'}${ids.length}个商品`
        };
      }
    },

    // 更新库存
    updateStock: async (id: number, quantity: number): Promise<ApiResponse> => {
      try {
        const response = await api.put(`/products/${id}/stock`, { quantity });
        return response.data;
      } catch (error: any) {
        // 模拟库存更新
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          message: '库存更新成功'
        };
      }
    }
  },

  // 商品规格管理
  variants: {
    // 获取规格列表
    getList: async (productId: number): Promise<ApiResponse<ProductVariant[]>> => {
      try {
        const response = await api.get(`/products/${productId}/variants`);
        return response.data;
      } catch (error: any) {
        // 模拟数据
        await new Promise(resolve => setTimeout(resolve, 300));
        return {
          success: true,
          message: '获取成功',
          data: []
        };
      }
    },

    // 创建规格
    create: async (productId: number, data: Partial<ProductVariant>): Promise<ApiResponse<ProductVariant>> => {
      try {
        const response = await api.post(`/products/${productId}/variants`, data);
        return response.data;
      } catch (error: any) {
        // 模拟创建
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          message: '创建成功',
          data: {
            id: Date.now(),
            product_id: productId,
            name: data.name!,
            description: data.description,
            price_adjustment: data.price_adjustment || 0,
            stock_quantity: data.stock_quantity || 0,
            is_active: data.is_active !== false,
            sort_order: data.sort_order || 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        };
      }
    },

    // 更新规格
    update: async (productId: number, id: number, data: Partial<ProductVariant>): Promise<ApiResponse<ProductVariant>> => {
      try {
        const response = await api.put(`/products/${productId}/variants/${id}`, data);
        return response.data;
      } catch (error: any) {
        // 模拟更新
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          message: '更新成功',
          data: {
            id,
            product_id: productId,
            name: data.name!,
            description: data.description,
            price_adjustment: data.price_adjustment || 0,
            stock_quantity: data.stock_quantity || 0,
            is_active: data.is_active !== false,
            sort_order: data.sort_order || 0,
            created_at: '2024-12-12 10:00:00',
            updated_at: new Date().toISOString()
          }
        };
      }
    },

    // 删除规格
    delete: async (productId: number, id: number): Promise<ApiResponse> => {
      try {
        const response = await api.delete(`/products/${productId}/variants/${id}`);
        return response.data;
      } catch (error: any) {
        // 模拟删除
        await new Promise(resolve => setTimeout(resolve, 300));
        return {
          success: true,
          message: '删除成功'
        };
      }
    }
  },

  // 文件上传
  upload: {
    // 上传图片
    uploadImage: async (file: File): Promise<ApiResponse<{ url: string }>> => {
      try {
        const formData = new FormData();
        formData.append('file', file);
        const response = await api.post('/upload/image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        return response.data;
      } catch (error: any) {
        // 模拟上传
        await new Promise(resolve => setTimeout(resolve, 1000));
        return {
          success: true,
          message: '上传成功',
          data: {
            url: `/images/products/mock_${Date.now()}.jpg`
          }
        };
      }
    }
  }
};
