// 商家相关类型定义

export interface Merchant {
  id: number;
  name: string;
  business_name: string;
  business_license: string;
  contact_person: string;
  contact_phone: string;
  email?: string;
  province: string;
  city: string;
  district: string;
  address: string;
  latitude?: number;
  longitude?: number;
  business_type: string;
  description?: string;
  logo?: string;
  banner_images?: string[];
  opening_hours?: any;
  delivery_fee: number;
  min_order_amount: number;
  delivery_radius: number;
  status: string;
  is_active: boolean;
  is_featured: boolean;
  rating: number;
  rating_count: number;
  commission_rate: number;
  balance: number;
  created_at: string;
  updated_at: string;
  approved_at?: string;
}

export interface MerchantUser {
  id: number;
  merchant_id: number;
  username: string;
  email: string;
  phone?: string;
  role: string;
  permissions?: string[];
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

// 商品相关类型定义

export interface Category {
  id: number;
  merchant_id: number;
  name: string;
  description?: string;
  image?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  product_count: number;
}

export interface Product {
  id: number;
  merchant_id: number;
  category_id: number;
  name: string;
  description?: string;
  images?: string[];
  price: number;
  original_price?: number;
  cost_price?: number;
  stock_quantity: number;
  min_stock: number;
  max_stock: number;
  sales_count: number;
  view_count: number;
  favorite_count: number;
  unit: string;
  weight?: number;
  calories?: number;
  ingredients?: string;
  allergens?: string;
  tags?: string[];
  is_spicy: boolean;
  is_vegetarian: boolean;
  is_recommended: boolean;
  is_new: boolean;
  status: string;
  sort_order: number;
  rating: number;
  rating_count: number;
  created_at: string;
  updated_at: string;
  category_name?: string;
  variants?: ProductVariant[];
}

export interface ProductVariant {
  id: number;
  product_id: number;
  name: string;
  description?: string;
  price_adjustment: number;
  stock_quantity: number;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 订单相关类型定义

export interface Order {
  id: number;
  order_number: string;
  merchant_id: number;
  user_id: number;
  status: string;
  delivery_type: string;
  delivery_address?: string;
  delivery_phone?: string;
  delivery_name?: string;
  delivery_latitude?: number;
  delivery_longitude?: number;
  delivery_distance?: number;
  delivery_person_id?: number;
  delivery_person_name?: string;
  delivery_person_phone?: string;
  subtotal: number;
  delivery_fee: number;
  discount_amount: number;
  total_amount: number;
  paid_amount: number;
  payment_method?: string;
  payment_status: string;
  payment_time?: string;
  payment_transaction_id?: string;
  estimated_delivery_time?: string;
  actual_delivery_time?: string;
  preparation_time: number;
  customer_note?: string;
  merchant_note?: string;
  cancel_reason?: string;
  is_rated: boolean;
  rating?: number;
  review?: string;
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  completed_at?: string;
  items: OrderItem[];
  merchant_name?: string;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  variant_id?: number;
  product_name: string;
  product_image?: string;
  variant_name?: string;
  unit_price: number;
  quantity: number;
  subtotal: number;
  note?: string;
  created_at: string;
}

// 退款相关类型定义

export interface OrderRefund {
  id: number;
  order_id: number;
  refund_number: string;
  refund_type: string;
  refund_reason: string;
  refund_amount: number;
  applicant_id: number;
  applicant_type: string;
  application_reason: string;
  evidence_images?: string[];
  status: string;
  handler_id?: number;
  handler_note?: string;
  reject_reason?: string;
  refund_method?: string;
  refund_account?: string;
  refund_transaction_id?: string;
  created_at: string;
  updated_at: string;
  processed_at?: string;
  completed_at?: string;
  items: RefundItem[];
  order_number?: string;
}

export interface RefundItem {
  id: number;
  refund_id: number;
  order_item_id: number;
  product_name: string;
  variant_name?: string;
  refund_quantity: number;
  refund_amount: number;
  reason?: string;
  created_at: string;
}

// 评价相关类型定义

export interface Review {
  id: number;
  merchant_id: number;
  order_id: number;
  customer_id: number;
  review_type: string;
  rating: number;
  content?: string;
  images?: string[];
  food_rating?: number;
  service_rating?: number;
  delivery_rating?: number;
  tags?: string[];
  merchant_reply?: string;
  reply_time?: string;
  replied_by?: number;
  is_anonymous: boolean;
  is_visible: boolean;
  is_featured: boolean;
  helpful_count: number;
  report_count: number;
  created_at: string;
  updated_at: string;
}

// 配送相关类型定义

export interface DeliveryPerson {
  id: number;
  merchant_id: number;
  name: string;
  phone: string;
  id_card: string;
  avatar?: string;
  employee_id?: string;
  vehicle_type: string;
  vehicle_number?: string;
  status: string;
  is_active: boolean;
  current_latitude?: number;
  current_longitude?: number;
  last_location_update?: string;
  total_orders: number;
  completed_orders: number;
  rating: number;
  rating_count: number;
  created_at: string;
  updated_at: string;
  last_online?: string;
}

// 通知相关类型定义

export interface Notification {
  id: number;
  merchant_id: number;
  type: string;
  priority: string;
  title: string;
  content: string;
  related_id?: number;
  related_type?: string;
  action_url?: string;
  action_text?: string;
  is_read: boolean;
  is_sent: boolean;
  send_email: boolean;
  send_sms: boolean;
  send_push: boolean;
  created_at: string;
  read_at?: string;
  sent_at?: string;
}

// API响应类型

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  total?: number;
  page?: number;
  per_page?: number;
}

// 分页参数

export interface PaginationParams {
  page?: number;
  per_page?: number;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// 统计数据类型

export interface DashboardStats {
  total_orders: number;
  pending_orders: number;
  today_revenue: number;
  month_revenue: number;
  total_products: number;
  low_stock_products: number;
  total_reviews: number;
  average_rating: number;
}

export interface SalesData {
  date: string;
  revenue: number;
  orders: number;
}

export interface TopProduct {
  id: number;
  name: string;
  sales_count: number;
  revenue: number;
}
