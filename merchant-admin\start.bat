@echo off
chcp 65001 >nul
echo ========================================
echo 商家管理系统启动脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    echo 请访问 https://www.python.org/downloads/ 下载安装Python
    pause
    exit /b 1
)

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js 16+
    echo 请访问 https://nodejs.org/ 下载安装Node.js
    pause
    exit /b 1
)

echo 环境检查通过
echo.

:: 启动后端
echo 启动后端服务...
cd backend
if not exist venv (
    echo 创建Python虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo 创建虚拟环境失败，请检查Python安装
        pause
        exit /b 1
    )
)

echo 激活虚拟环境...
call venv\Scripts\activate
if %errorlevel% neq 0 (
    echo 激活虚拟环境失败
    pause
    exit /b 1
)

echo 安装Python依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 安装依赖失败，请检查网络连接
    pause
    exit /b 1
)

echo 初始化数据库...
python -c "from app import db; db.create_all(); print('数据库初始化完成')"
if %errorlevel% neq 0 (
    echo 数据库初始化失败
    pause
    exit /b 1
)

echo 启动后端服务器...
start "商家管理系统-后端" cmd /k "call venv\Scripts\activate && python app.py"

cd ..

:: 启动前端
echo 📦 启动前端服务...
cd frontend

if not exist node_modules (
    echo 📦 安装前端依赖...
    npm install
)

echo 🚀 启动前端开发服务器...
start "商家管理系统-前端" cmd /k "npm start"

cd ..

echo.
echo ========================================
echo 🎉 商家管理系统启动完成！
echo ========================================
echo 📱 前端地址: http://localhost:3000
echo 🔧 后端地址: http://localhost:5001
echo 👤 演示账户: admin / 123456
echo ========================================
echo.
echo 按任意键关闭此窗口...
pause >nul
