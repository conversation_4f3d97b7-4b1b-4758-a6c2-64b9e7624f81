#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家管理系统启动脚本
"""

import os
import sys
import subprocess
import time
import platform
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "="*50)
    print(f"🔧 {title}")
    print("="*50)

def print_step(description):
    """打印步骤"""
    print(f"\n📋 {description}")

def print_success(message):
    """打印成功信息"""
    print(f"✅ {message}")

def print_error(message):
    """打印错误信息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息"""
    print(f"ℹ️  {message}")

def check_command(command, name):
    """检查命令是否可用"""
    try:
        result = subprocess.run([command, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip().split('\n')[0]
            print_success(f"{name}: {version}")
            return True
        else:
            print_error(f"{name} 未安装或不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        print_error(f"{name} 未安装或不可用")
        return False

def run_command(command, cwd=None, shell=False):
    """运行命令"""
    try:
        if platform.system() == "Windows":
            shell = True
        
        result = subprocess.run(command, cwd=cwd, shell=shell, 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            return True, result.stdout
        else:
            return False, result.stderr
    except subprocess.TimeoutExpired:
        return False, "命令执行超时"
    except Exception as e:
        return False, str(e)

def setup_backend():
    """设置后端"""
    print_header("设置后端环境")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print_error("backend目录不存在")
        return False
    
    # 创建虚拟环境
    venv_dir = backend_dir / "venv"
    if not venv_dir.exists():
        print_step("创建Python虚拟环境")
        success, output = run_command([sys.executable, "-m", "venv", "venv"], 
                                    cwd=backend_dir)
        if success:
            print_success("虚拟环境创建成功")
        else:
            print_error(f"虚拟环境创建失败: {output}")
            return False
    else:
        print_info("虚拟环境已存在")
    
    # 安装基础依赖
    print_step("安装Python依赖")
    
    if platform.system() == "Windows":
        pip_path = venv_dir / "Scripts" / "pip.exe"
        python_path = venv_dir / "Scripts" / "python.exe"
    else:
        pip_path = venv_dir / "bin" / "pip"
        python_path = venv_dir / "bin" / "python"
    
    # 安装基础包
    basic_packages = [
        "flask==2.3.3",
        "flask-sqlalchemy==3.0.5",
        "flask-jwt-extended==4.5.3",
        "flask-cors==4.0.0",
        "werkzeug==2.3.7"
    ]
    
    for package in basic_packages:
        success, output = run_command([str(pip_path), "install", package])
        if not success:
            print_error(f"安装 {package} 失败: {output}")
    
    print_success("基础依赖安装完成")
    
    # 初始化数据库
    print_step("初始化数据库")
    init_code = """
import sys
sys.path.append('.')
try:
    from app import app, db
    with app.app_context():
        db.create_all()
        print('数据库初始化成功')
except Exception as e:
    print(f'数据库初始化失败: {e}')
"""
    
    success, output = run_command([str(python_path), "-c", init_code], 
                                cwd=backend_dir)
    if success:
        print_success("数据库初始化成功")
    else:
        print_info(f"数据库初始化: {output}")
    
    return True

def setup_frontend():
    """设置前端"""
    print_header("设置前端环境")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print_error("frontend目录不存在")
        return False
    
    # 检查node_modules
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print_step("安装前端依赖")
        success, output = run_command(["npm", "install"], cwd=frontend_dir)
        if success:
            print_success("前端依赖安装成功")
        else:
            print_error(f"前端依赖安装失败: {output}")
            return False
    else:
        print_info("前端依赖已安装")
    
    return True

def start_services():
    """启动服务"""
    print_header("启动服务")
    
    # 启动后端
    print_step("启动后端服务")
    backend_dir = Path("backend")
    
    if platform.system() == "Windows":
        python_path = backend_dir / "venv" / "Scripts" / "python.exe"
        cmd = f'cd /d "{backend_dir.absolute()}" && "{python_path}" app.py'
        subprocess.Popen(cmd, shell=True, creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:
        python_path = backend_dir / "venv" / "bin" / "python"
        subprocess.Popen([str(python_path), "app.py"], cwd=backend_dir)
    
    print_success("后端服务启动中...")
    
    # 等待后端启动
    time.sleep(3)
    
    # 启动前端
    print_step("启动前端服务")
    frontend_dir = Path("frontend")
    
    if platform.system() == "Windows":
        cmd = f'cd /d "{frontend_dir.absolute()}" && npm start'
        subprocess.Popen(cmd, shell=True, creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:
        subprocess.Popen(["npm", "start"], cwd=frontend_dir)
    
    print_success("前端服务启动中...")

def main():
    """主函数"""
    print_header("商家管理系统启动")
    
    # 检查环境
    print_step("检查运行环境")
    
    if not check_command("python", "Python"):
        print_error("请安装Python 3.8+")
        input("按回车键退出...")
        return
    
    if not check_command("node", "Node.js"):
        print_error("请安装Node.js 16+")
        input("按回车键退出...")
        return
    
    if not check_command("npm", "npm"):
        print_error("npm不可用")
        input("按回车键退出...")
        return
    
    # 检查目录
    if not Path("backend").exists() or not Path("frontend").exists():
        print_error("请在merchant-admin目录下运行此脚本")
        input("按回车键退出...")
        return
    
    # 设置环境
    if not setup_backend():
        print_error("后端环境设置失败")
        input("按回车键退出...")
        return
    
    if not setup_frontend():
        print_error("前端环境设置失败")
        input("按回车键退出...")
        return
    
    # 启动服务
    start_services()
    
    # 显示信息
    print_header("启动完成")
    print_success("🎉 商家管理系统启动成功！")
    print_info("📱 前端地址: http://localhost:3000")
    print_info("🔧 后端地址: http://localhost:5001")
    print_info("👤 演示账户: admin / 123456")
    print("\n请等待浏览器自动打开，或手动访问前端地址")
    print("按回车键退出...")
    input()

if __name__ == "__main__":
    main()
