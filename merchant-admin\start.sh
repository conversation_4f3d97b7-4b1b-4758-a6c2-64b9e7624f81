#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}🚀 商家管理系统启动脚本${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Python，请先安装Python 3.8+${NC}"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Node.js，请先安装Node.js 16+${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境检查通过${NC}"
echo

# 启动后端
echo -e "${YELLOW}📦 启动后端服务...${NC}"
cd backend

if [ ! -d "venv" ]; then
    echo -e "${YELLOW}🔧 创建Python虚拟环境...${NC}"
    python3 -m venv venv
fi

echo -e "${YELLOW}🔧 激活虚拟环境...${NC}"
source venv/bin/activate

echo -e "${YELLOW}📦 安装Python依赖...${NC}"
pip install -r requirements.txt

echo -e "${YELLOW}🗄️ 初始化数据库...${NC}"
python3 -c "from app import db; db.create_all(); print('数据库初始化完成')"

echo -e "${YELLOW}🚀 启动后端服务器...${NC}"
python3 app.py &
BACKEND_PID=$!

cd ..

# 启动前端
echo -e "${YELLOW}📦 启动前端服务...${NC}"
cd frontend

if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 安装前端依赖...${NC}"
    npm install
fi

echo -e "${YELLOW}🚀 启动前端开发服务器...${NC}"
npm start &
FRONTEND_PID=$!

cd ..

echo
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}🎉 商家管理系统启动完成！${NC}"
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}📱 前端地址: http://localhost:3000${NC}"
echo -e "${GREEN}🔧 后端地址: http://localhost:5001${NC}"
echo -e "${GREEN}👤 演示账户: admin / 123456${NC}"
echo -e "${GREEN}========================================${NC}"
echo

# 等待用户输入来停止服务
echo -e "${YELLOW}按 Ctrl+C 停止所有服务...${NC}"

# 捕获中断信号
trap 'echo -e "\n${YELLOW}正在停止服务...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# 等待进程
wait
