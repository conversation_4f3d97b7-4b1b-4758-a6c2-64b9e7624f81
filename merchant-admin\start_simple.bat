@echo off
echo ========================================
echo 商家管理系统启动脚本
echo ========================================
echo.

:: 检查当前目录
if not exist "backend" (
    echo 错误: 请在merchant-admin目录下运行此脚本
    pause
    exit /b 1
)

if not exist "frontend" (
    echo 错误: 请在merchant-admin目录下运行此脚本
    pause
    exit /b 1
)

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python
    echo 请安装Python 3.8+ 从 https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js
    echo 请安装Node.js 16+ 从 https://nodejs.org/
    pause
    exit /b 1
)

echo 环境检查通过
echo.

:: 设置后端
echo 设置后端环境...
cd backend

if not exist venv (
    echo 创建虚拟环境...
    python -m venv venv
)

echo 激活虚拟环境并安装依赖...
call venv\Scripts\activate.bat
pip install flask flask-sqlalchemy flask-jwt-extended flask-cors werkzeug

echo 启动后端...
start "后端服务" cmd /k "cd /d %cd% && call venv\Scripts\activate.bat && python app.py"

cd ..

:: 等待后端启动
echo 等待后端启动...
timeout /t 3 /nobreak >nul

:: 设置前端
echo 设置前端环境...
cd frontend

if not exist node_modules (
    echo 安装前端依赖...
    npm install
)

echo 启动前端...
start "前端服务" cmd /k "cd /d %cd% && npm start"

cd ..

echo.
echo ========================================
echo 启动完成！
echo ========================================
echo 前端: http://localhost:3000
echo 后端: http://localhost:5001
echo 账户: admin / 123456
echo ========================================
echo.
echo 请等待浏览器自动打开，或手动访问前端地址
pause
