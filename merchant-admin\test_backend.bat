@echo off
echo ========================================
echo 测试后端服务启动
echo ========================================
echo.

cd backend

echo 检查Python...
python --version
if %errorlevel% neq 0 (
    echo Python未安装
    pause
    exit /b 1
)

echo.
echo 创建虚拟环境...
if not exist venv (
    python -m venv venv
)

echo.
echo 激活虚拟环境...
call venv\Scripts\activate.bat

echo.
echo 安装基础依赖...
pip install flask flask-sqlalchemy flask-jwt-extended flask-cors werkzeug

echo.
echo 启动测试后端...
python test_app.py

pause
