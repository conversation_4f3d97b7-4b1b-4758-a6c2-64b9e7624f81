#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家管理系统测试脚本
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🔧 {title}")
    print("="*60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")

def print_success(message):
    """打印成功信息"""
    print(f"✅ {message}")

def print_error(message):
    """打印错误信息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息"""
    print(f"ℹ️  {message}")

def check_requirements():
    """检查系统要求"""
    print_header("检查系统要求")
    
    # 检查Python
    try:
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            print_success(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        else:
            print_error(f"Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}，需要3.8+")
            return False
    except Exception as e:
        print_error(f"Python检查失败: {str(e)}")
        return False
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            node_version = result.stdout.strip()
            print_success(f"Node.js版本: {node_version}")
        else:
            print_error("Node.js未安装或不在PATH中")
            return False
    except Exception as e:
        print_error(f"Node.js检查失败: {str(e)}")
        return False
    
    # 检查npm
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            npm_version = result.stdout.strip()
            print_success(f"npm版本: {npm_version}")
        else:
            print_error("npm未安装或不在PATH中")
            return False
    except Exception as e:
        print_error(f"npm检查失败: {str(e)}")
        return False
    
    return True

def setup_backend():
    """设置后端环境"""
    print_header("设置后端环境")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print_error("backend目录不存在")
        return False
    
    os.chdir(backend_dir)
    
    # 创建虚拟环境
    print_step(1, "创建Python虚拟环境")
    if not Path("venv").exists():
        try:
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
            print_success("虚拟环境创建成功")
        except subprocess.CalledProcessError as e:
            print_error(f"虚拟环境创建失败: {str(e)}")
            return False
    else:
        print_info("虚拟环境已存在")
    
    # 激活虚拟环境并安装依赖
    print_step(2, "安装Python依赖")
    try:
        if os.name == 'nt':  # Windows
            pip_path = Path("venv/Scripts/pip.exe")
        else:  # Unix/Linux/macOS
            pip_path = Path("venv/bin/pip")
        
        if pip_path.exists():
            subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], check=True)
            print_success("Python依赖安装成功")
        else:
            print_error("pip路径不存在")
            return False
    except subprocess.CalledProcessError as e:
        print_error(f"Python依赖安装失败: {str(e)}")
        return False
    
    # 初始化数据库
    print_step(3, "初始化数据库")
    try:
        if os.name == 'nt':  # Windows
            python_path = Path("venv/Scripts/python.exe")
        else:  # Unix/Linux/macOS
            python_path = Path("venv/bin/python")
        
        if python_path.exists():
            subprocess.run([str(python_path), "-c", "from app import db; db.create_all(); print('数据库初始化完成')"], check=True)
            print_success("数据库初始化成功")
        else:
            print_error("Python路径不存在")
            return False
    except subprocess.CalledProcessError as e:
        print_error(f"数据库初始化失败: {str(e)}")
        return False
    
    os.chdir("..")
    return True

def setup_frontend():
    """设置前端环境"""
    print_header("设置前端环境")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print_error("frontend目录不存在")
        return False
    
    os.chdir(frontend_dir)
    
    # 安装前端依赖
    print_step(1, "安装前端依赖")
    if not Path("node_modules").exists():
        try:
            subprocess.run(["npm", "install"], check=True)
            print_success("前端依赖安装成功")
        except subprocess.CalledProcessError as e:
            print_error(f"前端依赖安装失败: {str(e)}")
            return False
    else:
        print_info("前端依赖已安装")
    
    os.chdir("..")
    return True

def start_backend():
    """启动后端服务"""
    print_header("启动后端服务")
    
    backend_dir = Path("backend")
    os.chdir(backend_dir)
    
    try:
        if os.name == 'nt':  # Windows
            python_path = Path("venv/Scripts/python.exe")
        else:  # Unix/Linux/macOS
            python_path = Path("venv/bin/python")
        
        print_info("正在启动后端服务器...")
        process = subprocess.Popen([str(python_path), "app.py"])
        
        # 等待服务器启动
        time.sleep(5)
        
        # 检查服务器是否启动成功
        try:
            response = requests.get("http://localhost:5001/api/health", timeout=5)
            if response.status_code == 200:
                print_success("后端服务启动成功")
                print_info("后端地址: http://localhost:5001")
                os.chdir("..")
                return process
            else:
                print_error(f"后端服务启动失败，状态码: {response.status_code}")
                process.terminate()
                os.chdir("..")
                return None
        except requests.exceptions.RequestException as e:
            print_error(f"后端服务连接失败: {str(e)}")
            process.terminate()
            os.chdir("..")
            return None
            
    except Exception as e:
        print_error(f"后端服务启动失败: {str(e)}")
        os.chdir("..")
        return None

def start_frontend():
    """启动前端服务"""
    print_header("启动前端服务")
    
    frontend_dir = Path("frontend")
    os.chdir(frontend_dir)
    
    try:
        print_info("正在启动前端开发服务器...")
        process = subprocess.Popen(["npm", "start"])
        
        # 等待前端服务器启动
        time.sleep(10)
        
        # 检查前端服务器是否启动成功
        try:
            response = requests.get("http://localhost:3000", timeout=5)
            if response.status_code == 200:
                print_success("前端服务启动成功")
                print_info("前端地址: http://localhost:3000")
                os.chdir("..")
                return process
            else:
                print_error(f"前端服务启动失败，状态码: {response.status_code}")
                process.terminate()
                os.chdir("..")
                return None
        except requests.exceptions.RequestException as e:
            print_error(f"前端服务连接失败: {str(e)}")
            process.terminate()
            os.chdir("..")
            return None
            
    except Exception as e:
        print_error(f"前端服务启动失败: {str(e)}")
        os.chdir("..")
        return None

def test_api():
    """测试API接口"""
    print_header("测试API接口")
    
    # 测试健康检查接口
    print_step(1, "测试健康检查接口")
    try:
        response = requests.get("http://localhost:5001/api/health")
        if response.status_code == 200:
            data = response.json()
            print_success(f"健康检查通过: {data.get('message', 'OK')}")
        else:
            print_error(f"健康检查失败，状态码: {response.status_code}")
    except Exception as e:
        print_error(f"健康检查失败: {str(e)}")
    
    # 测试登录接口
    print_step(2, "测试登录接口")
    try:
        login_data = {
            "username": "admin",
            "password": "123456"
        }
        response = requests.post("http://localhost:5001/api/auth/merchant/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print_success("登录测试成功")
                print_info(f"用户: {data['data']['user']['username']}")
                print_info(f"商家: {data['data']['merchant']['name']}")
            else:
                print_error(f"登录失败: {data.get('message', '未知错误')}")
        else:
            print_error(f"登录请求失败，状态码: {response.status_code}")
    except Exception as e:
        print_error(f"登录测试失败: {str(e)}")

def main():
    """主函数"""
    print_header("商家管理系统测试")
    print_info("开始系统测试...")
    
    # 检查系统要求
    if not check_requirements():
        print_error("系统要求检查失败，请安装必要的软件")
        return
    
    # 设置后端环境
    if not setup_backend():
        print_error("后端环境设置失败")
        return
    
    # 设置前端环境
    if not setup_frontend():
        print_error("前端环境设置失败")
        return
    
    # 启动后端服务
    backend_process = start_backend()
    if not backend_process:
        print_error("后端服务启动失败")
        return
    
    # 启动前端服务
    frontend_process = start_frontend()
    if not frontend_process:
        print_error("前端服务启动失败")
        if backend_process:
            backend_process.terminate()
        return
    
    # 测试API接口
    test_api()
    
    # 显示系统信息
    print_header("系统启动完成")
    print_success("🎉 商家管理系统启动成功！")
    print_info("📱 前端地址: http://localhost:3000")
    print_info("🔧 后端地址: http://localhost:5001")
    print_info("👤 演示账户: admin / 123456")
    print_info("📚 API文档: http://localhost:5001/docs")
    print("\n按 Ctrl+C 停止所有服务...")
    
    try:
        # 等待用户中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print_info("\n正在停止服务...")
        if frontend_process:
            frontend_process.terminate()
        if backend_process:
            backend_process.terminate()
        print_success("所有服务已停止")

if __name__ == "__main__":
    main()
