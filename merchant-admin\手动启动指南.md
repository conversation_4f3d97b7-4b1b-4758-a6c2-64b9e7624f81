# 🚀 商家管理系统 - 手动启动指南

## 📋 前置要求
- Python 3.8+ 已安装
- Node.js 16+ 已安装

## 🔧 手动启动步骤

### 第一步：启动后端

1. **打开命令提示符**
   ```
   按 Win + R，输入 cmd，按回车
   ```

2. **进入后端目录**
   ```bash
   cd /d "你的路径\merchant-admin\backend"
   ```

3. **创建虚拟环境**（首次运行）
   ```bash
   python -m venv venv
   ```

4. **激活虚拟环境**
   ```bash
   venv\Scripts\activate
   ```

5. **安装依赖**（首次运行）
   ```bash
   pip install flask flask-sqlalchemy flask-jwt-extended flask-cors werkzeug
   ```

6. **启动后端**
   ```bash
   python app.py
   ```

### 第二步：启动前端

1. **打开新的命令提示符**
   ```
   按 Win + R，输入 cmd，按回车
   ```

2. **进入前端目录**
   ```bash
   cd /d "你的路径\merchant-admin\frontend"
   ```

3. **安装依赖**（首次运行）
   ```bash
   npm install
   ```

4. **启动前端**
   ```bash
   npm start
   ```

## 🌐 访问系统

- **前端地址**: http://localhost:3000
- **后端地址**: http://localhost:5001
- **演示账户**: admin / 123456

## 🎯 快速启动方式

### 方式1：使用Python脚本
```bash
python start.py
```

### 方式2：使用简化批处理
```bash
start_simple.bat
```

## ❗ 常见问题

### 1. Python命令不识别
- 确保Python已正确安装
- 检查Python是否添加到系统PATH

### 2. Node命令不识别
- 确保Node.js已正确安装
- 重启命令提示符

### 3. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :5001

# 结束进程
taskkill /PID <进程ID> /F
```

### 4. 依赖安装失败
- 检查网络连接
- 尝试使用国内镜像：
  ```bash
  pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flask
  npm config set registry https://registry.npm.taobao.org
  ```

## 📞 获取帮助

如果遇到问题：
1. 检查Python和Node.js版本
2. 确保在正确的目录下运行命令
3. 查看错误信息并根据提示解决

---

**祝您使用愉快！** 🎉
