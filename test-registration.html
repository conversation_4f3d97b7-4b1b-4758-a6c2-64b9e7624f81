<!DOCTYPE html>
<html>
<head>
    <title>测试注册功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>注册功能测试</h1>
    
    <div class="test-section">
        <h2>邮箱注册测试</h2>
        <button onclick="testEmailRegister()">测试邮箱注册</button>
        <div id="emailResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>手机号注册测试</h2>
        <button onclick="testPhoneRegister()">测试手机号注册</button>
        <div id="phoneResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>重复注册测试</h2>
        <button onclick="testDuplicateEmail()">测试重复邮箱注册</button>
        <button onclick="testDuplicatePhone()">测试重复手机号注册</button>
        <div id="duplicateResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        
        async function testEmailRegister() {
            const resultDiv = document.getElementById('emailResult');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/register/email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456',
                        username: 'testuser'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `✅ 邮箱注册成功: ${data.message}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `❌ 邮箱注册失败: ${data.message}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testPhoneRegister() {
            const resultDiv = document.getElementById('phoneResult');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/register/phone`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phone: '13800138000',
                        password: '123456',
                        username: 'phoneuser'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `✅ 手机号注册成功: ${data.message}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `❌ 手机号注册失败: ${data.message}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testDuplicateEmail() {
            const resultDiv = document.getElementById('duplicateResult');
            resultDiv.innerHTML = '测试重复邮箱注册...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/register/email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456',
                        username: 'testuser2'
                    })
                });
                
                const data = await response.json();
                
                if (!data.success && data.message.includes('邮箱已被注册')) {
                    resultDiv.innerHTML = `✅ 重复邮箱检测正常: ${data.message}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `❌ 重复邮箱检测异常: ${data.message}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testDuplicatePhone() {
            const resultDiv = document.getElementById('duplicateResult');
            resultDiv.innerHTML = '测试重复手机号注册...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/register/phone`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phone: '13800138000',
                        password: '123456',
                        username: 'phoneuser2'
                    })
                });
                
                const data = await response.json();
                
                if (!data.success && data.message.includes('手机号已被注册')) {
                    resultDiv.innerHTML = `✅ 重复手机号检测正常: ${data.message}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `❌ 重复手机号检测异常: ${data.message}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
