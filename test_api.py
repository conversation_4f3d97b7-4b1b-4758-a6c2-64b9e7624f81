#!/usr/bin/env python3
"""
测试注册和登录API的脚本
"""

import requests
import json

API_BASE = 'http://localhost:5000/api'

def test_email_register():
    """测试邮箱注册"""
    print("=== 测试邮箱注册 ===")
    
    url = f"{API_BASE}/auth/register/email"
    data = {
        "email": "<EMAIL>",
        "password": "123456",
        "username": "testuser"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_phone_register():
    """测试手机号注册"""
    print("\n=== 测试手机号注册 ===")
    
    url = f"{API_BASE}/auth/register/phone"
    data = {
        "phone": "13800138000",
        "password": "123456",
        "username": "phoneuser"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_duplicate_email():
    """测试重复邮箱注册"""
    print("\n=== 测试重复邮箱注册 ===")
    
    url = f"{API_BASE}/auth/register/email"
    data = {
        "email": "<EMAIL>",
        "password": "123456",
        "username": "testuser2"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_email_login():
    """测试邮箱登录"""
    print("\n=== 测试邮箱登录 ===")
    
    url = f"{API_BASE}/auth/login/email"
    data = {
        "email": "<EMAIL>",
        "password": "123456"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_send_sms():
    """测试发送短信验证码"""
    print("\n=== 测试发送短信验证码 ===")
    
    url = f"{API_BASE}/auth/send-sms-code"
    data = {
        "phone": "13800138001"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_api_status():
    """测试API状态"""
    print("=== 测试API状态 ===")
    
    try:
        response = requests.get(f"http://localhost:5000/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

if __name__ == "__main__":
    print("开始测试API...")
    
    # 测试API状态
    test_api_status()
    
    # 测试注册
    test_email_register()
    test_phone_register()
    test_duplicate_email()
    
    # 测试登录
    test_email_login()
    
    # 测试发送验证码
    test_send_sms()
    
    print("\n测试完成！")
