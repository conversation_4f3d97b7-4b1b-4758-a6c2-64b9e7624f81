#!/usr/bin/env python3
"""
注册登录系统测试脚本
用于验证修复后的认证系统是否正常工作
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:5001/api"
TEST_EMAIL = "<EMAIL>"
TEST_PHONE = "13800138000"
TEST_PASSWORD = "123456"
TEST_USERNAME = "testuser"

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端健康状态...")
    try:
        response = requests.get(f"{BASE_URL.replace('/api', '')}/")
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保后端已启动")
        return False

def test_email_register():
    """测试邮箱注册"""
    print("\n📧 测试邮箱注册...")
    data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "username": TEST_USERNAME
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/register/email", json=data)
        result = response.json()
        
        if response.status_code == 201 and result.get("success"):
            print("✅ 邮箱注册成功")
            return True
        else:
            print(f"❌ 邮箱注册失败: {result.get('message', '未知错误')}")
            return False
    except Exception as e:
        print(f"❌ 邮箱注册异常: {e}")
        return False

def test_phone_register():
    """测试手机号注册"""
    print("\n📱 测试手机号注册...")
    data = {
        "phone": TEST_PHONE,
        "password": TEST_PASSWORD,
        "username": f"{TEST_USERNAME}_phone"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/register/phone", json=data)
        result = response.json()
        
        if response.status_code == 201 and result.get("success"):
            print("✅ 手机号注册成功")
            return True
        else:
            print(f"❌ 手机号注册失败: {result.get('message', '未知错误')}")
            return False
    except Exception as e:
        print(f"❌ 手机号注册异常: {e}")
        return False

def test_email_login():
    """测试邮箱登录"""
    print("\n🔐 测试邮箱登录...")
    data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/email", json=data)
        result = response.json()
        
        if response.status_code == 200 and result.get("success"):
            print("✅ 邮箱登录成功")
            token = result.get("access_token")
            refresh_token = result.get("refresh_token")
            return token, refresh_token
        else:
            print(f"❌ 邮箱登录失败: {result.get('message', '未知错误')}")
            return None, None
    except Exception as e:
        print(f"❌ 邮箱登录异常: {e}")
        return None, None

def test_phone_login():
    """测试手机号登录"""
    print("\n🔐 测试手机号登录...")
    data = {
        "phone": TEST_PHONE,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/phone", json=data)
        result = response.json()
        
        if response.status_code == 200 and result.get("success"):
            print("✅ 手机号登录成功")
            token = result.get("access_token")
            refresh_token = result.get("refresh_token")
            return token, refresh_token
        else:
            print(f"❌ 手机号登录失败: {result.get('message', '未知错误')}")
            return None, None
    except Exception as e:
        print(f"❌ 手机号登录异常: {e}")
        return None, None

def test_token_refresh(refresh_token):
    """测试token刷新"""
    print("\n🔄 测试token刷新...")
    data = {
        "refresh_token": refresh_token
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/refresh", json=data)
        result = response.json()
        
        if response.status_code == 200 and result.get("success"):
            print("✅ Token刷新成功")
            return result.get("access_token")
        else:
            print(f"❌ Token刷新失败: {result.get('message', '未知错误')}")
            return None
    except Exception as e:
        print(f"❌ Token刷新异常: {e}")
        return None

def test_get_profile(access_token):
    """测试获取用户信息"""
    print("\n👤 测试获取用户信息...")
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/auth/profile", headers=headers)
        result = response.json()
        
        if response.status_code == 200 and result.get("success"):
            print("✅ 获取用户信息成功")
            user = result.get("user", {})
            print(f"   用户ID: {user.get('id')}")
            print(f"   用户名: {user.get('username')}")
            print(f"   邮箱: {user.get('email')}")
            return True
        else:
            print(f"❌ 获取用户信息失败: {result.get('message', '未知错误')}")
            return False
    except Exception as e:
        print(f"❌ 获取用户信息异常: {e}")
        return False

def test_send_verification_codes():
    """测试发送验证码"""
    print("\n📨 测试发送验证码...")
    
    # 测试发送邮箱验证码
    try:
        response = requests.post(f"{BASE_URL}/auth/send-email-code", json={"email": TEST_EMAIL})
        result = response.json()
        if response.status_code == 200 and result.get("success"):
            print("✅ 邮箱验证码发送成功")
        else:
            print(f"❌ 邮箱验证码发送失败: {result.get('message', '未知错误')}")
    except Exception as e:
        print(f"❌ 邮箱验证码发送异常: {e}")
    
    # 测试发送短信验证码
    try:
        response = requests.post(f"{BASE_URL}/auth/send-sms-code", json={"phone": TEST_PHONE})
        result = response.json()
        if response.status_code == 200 and result.get("success"):
            print("✅ 短信验证码发送成功")
        else:
            print(f"❌ 短信验证码发送失败: {result.get('message', '未知错误')}")
    except Exception as e:
        print(f"❌ 短信验证码发送异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试注册登录系统...")
    print("=" * 50)
    
    # 测试后端健康状态
    if not test_backend_health():
        print("\n❌ 后端服务不可用，跳过其他测试")
        return
    
    # 测试注册功能
    test_email_register()
    test_phone_register()
    
    # 测试登录功能
    email_token, email_refresh = test_email_login()
    phone_token, phone_refresh = test_phone_login()
    
    # 测试token刷新
    if email_refresh:
        test_token_refresh(email_refresh)
    
    # 测试获取用户信息
    if email_token:
        test_get_profile(email_token)
    
    # 测试发送验证码
    test_send_verification_codes()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n💡 提示:")
    print("1. 如果后端服务未启动，请先运行: python backend/app.py")
    print("2. 如果前端需要测试，请运行: npm start (在frontend目录下)")
    print("3. 模拟验证码为: 123456")

if __name__ == "__main__":
    main() 